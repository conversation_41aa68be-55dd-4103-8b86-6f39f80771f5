# =============================================================================
# RK Institute Management System - Staging Environment Configuration
# =============================================================================
# 
# STAGING ENVIRONMENT TEMPLATE
# - Copy to .env.staging and fill in actual values
# - Use Vercel Environment Variables dashboard for staging secrets
# - This environment mirrors production for comprehensive testing
# 
# Version: 2.0
# Last Updated: 2025-07-02
# =============================================================================

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================
NODE_ENV=staging
PORT=3000
NEXT_PUBLIC_APP_URL=https://staging-rk-institute.vercel.app

# =============================================================================
# STAGING DATABASE CONFIGURATION
# =============================================================================
# Dedicated staging database (separate from production)
# Use Neon PostgreSQL or compatible provider
DATABASE_URL=*******************************************************************/staging_rk_institute?sslmode=require
DIRECT_URL=*******************************************************************/staging_rk_institute?sslmode=require

# Connection pooling settings for staging
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=10
DATABASE_TIMEOUT=30000

# Database backup configuration (staging-specific)
DATABASE_BACKUP_ENABLED=true
DATABASE_BACKUP_SCHEDULE="0 3 * * *"
DATABASE_BACKUP_RETENTION_DAYS=7

# =============================================================================
# STAGING SECURITY CONFIGURATION
# =============================================================================
# Staging-specific JWT secret (different from production)
# Generate with: openssl rand -base64 32
JWT_SECRET=STAGING_SECURE_32_PLUS_CHARACTER_SECRET_KEY_DIFFERENT_FROM_PROD
JWT_EXPIRY=4h

# Staging data encryption key (different from production)
# Generate with: openssl rand -hex 32
DATA_ENCRYPTION_KEY=STAGING_SECURE_64_CHARACTER_HEX_ENCRYPTION_KEY_DIFFERENT_FROM_PROD

# Password hashing (slightly lower for faster testing)
BCRYPT_ROUNDS=12

# Session configuration
SESSION_TIMEOUT=14400000
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_TIME=900000

# =============================================================================
# STAGING FEATURE FLAGS
# =============================================================================
# Enable all features for comprehensive testing
FEATURE_REPORTING=true
FEATURE_ANALYTICS=true
FEATURE_NOTIFICATIONS=true
FEATURE_ADVANCED_SEARCH=true
FEATURE_BULK_OPERATIONS=true
FEATURE_EXPORT_IMPORT=true
FEATURE_AUDIT_LOGGING=true
FEATURE_PERFORMANCE_MONITORING=true

# Staging-specific features for testing
FEATURE_DEBUG_MODE=true
FEATURE_VERBOSE_LOGGING=true
FEATURE_PERFORMANCE_PROFILING=true
FEATURE_SYNTHETIC_TESTING=true
FEATURE_TEST_DATA_GENERATION=true

# =============================================================================
# STAGING MONITORING CONFIGURATION
# =============================================================================
# Enhanced monitoring for staging validation
MONITORING_ENABLED=true
HEALTH_CHECK_INTERVAL=30000
PERFORMANCE_MONITORING=true
ERROR_TRACKING_ENABLED=true
SYNTHETIC_MONITORING=true

# Staging-specific monitoring
STAGING_VALIDATION_MODE=true
QUALITY_GATES_MONITORING=true
AUTOMATED_TESTING_ENABLED=true

# Monitoring thresholds (more lenient for staging)
RESPONSE_TIME_THRESHOLD=2000
MEMORY_THRESHOLD=512
CPU_THRESHOLD=80
ERROR_RATE_THRESHOLD=0.05

# =============================================================================
# STAGING TESTING CONFIGURATION
# =============================================================================
# Test data and scenarios
TEST_DATA_ENABLED=true
SYNTHETIC_USERS_ENABLED=true
LOAD_TESTING_ENABLED=true
SECURITY_TESTING_ENABLED=true

# Testing thresholds
PERFORMANCE_THRESHOLD_MS=2000
MEMORY_THRESHOLD_MB=512
CPU_THRESHOLD_PERCENT=80
ERROR_RATE_THRESHOLD=0.01

# Test user credentials (for automated testing)
TEST_ADMIN_EMAIL=<EMAIL>
TEST_TEACHER_EMAIL=<EMAIL>
TEST_STUDENT_EMAIL=<EMAIL>
TEST_USER_PASSWORD=StagingTest123!

# =============================================================================
# STAGING DEPLOYMENT CONFIGURATION
# =============================================================================
# Vercel staging configuration
VERCEL_ENV=preview
VERCEL_BRANCH=staging
VERCEL_URL=staging-rk-institute.vercel.app

# Deployment validation
DEPLOYMENT_VALIDATION_ENABLED=true
POST_DEPLOYMENT_TESTING=true
ROLLBACK_ON_FAILURE=true

# Function configuration (staging-optimized)
FUNCTION_TIMEOUT=30
FUNCTION_MEMORY=1024

# =============================================================================
# STAGING EMAIL CONFIGURATION
# =============================================================================
# Email testing (use staging email service)
EMAIL_ENABLED=true
EMAIL_PROVIDER=staging
EMAIL_FROM=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>

# SMTP Configuration for staging
SMTP_HOST=staging-smtp.example.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=staging_smtp_user
SMTP_PASS=staging_smtp_password

# =============================================================================
# STAGING FILE STORAGE CONFIGURATION
# =============================================================================
# File storage for staging environment
STORAGE_PROVIDER=local
STORAGE_PATH=./uploads/staging
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,doc,docx,jpg,jpeg,png

# =============================================================================
# STAGING LOGGING CONFIGURATION
# =============================================================================
# Enhanced logging for staging validation
LOG_LEVEL=debug
LOG_FORMAT=json
LOG_DESTINATION=console,file
LOG_FILE_PATH=./logs/staging.log
LOG_MAX_SIZE=10485760
LOG_MAX_FILES=5

# Staging-specific logging
LOG_PERFORMANCE=true
LOG_SECURITY_EVENTS=true
LOG_DATABASE_QUERIES=true
LOG_API_REQUESTS=true

# =============================================================================
# STAGING CACHE CONFIGURATION
# =============================================================================
# Cache configuration for staging
CACHE_ENABLED=true
CACHE_PROVIDER=memory
CACHE_TTL=300
CACHE_MAX_SIZE=100

# =============================================================================
# STAGING RATE LIMITING CONFIGURATION
# =============================================================================
# Rate limiting (more lenient for testing)
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX_REQUESTS=1000
RATE_LIMIT_SKIP_SUCCESSFUL=false

# =============================================================================
# STAGING CORS CONFIGURATION
# =============================================================================
# CORS settings for staging
CORS_ENABLED=true
CORS_ORIGIN=https://staging-rk-institute.vercel.app
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization,X-Requested-With

# =============================================================================
# STAGING SECURITY HEADERS CONFIGURATION
# =============================================================================
# Security headers (staging-specific)
CSP_ENABLED=true
CSP_REPORT_ONLY=false
CSP_REPORT_URI=/api/csp-report

# HSTS Configuration (staging)
HSTS_ENABLED=true
HSTS_MAX_AGE=31536000
HSTS_INCLUDE_SUBDOMAINS=true

# =============================================================================
# STAGING ANALYTICS CONFIGURATION
# =============================================================================
# Analytics for staging validation
ANALYTICS_ENABLED=true
ANALYTICS_PROVIDER=staging
ANALYTICS_TRACKING_ID=staging-tracking-id

# Performance analytics
PERFORMANCE_ANALYTICS=true
USER_ANALYTICS=false
ERROR_ANALYTICS=true

# =============================================================================
# STAGING NOTIFICATION CONFIGURATION
# =============================================================================
# Notifications for staging environment
NOTIFICATIONS_ENABLED=true
NOTIFICATION_CHANNELS=email,console

# Staging notification settings
NOTIFY_ON_DEPLOYMENT=true
NOTIFY_ON_ERRORS=true
NOTIFY_ON_PERFORMANCE_ISSUES=true

# =============================================================================
# STAGING BACKUP CONFIGURATION
# =============================================================================
# Backup settings for staging
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 3 * * *"
BACKUP_RETENTION_DAYS=7
BACKUP_DESTINATION=local
BACKUP_PATH=./backups/staging

# =============================================================================
# STAGING QUALITY GATES CONFIGURATION
# =============================================================================
# Quality gates specific to staging
QUALITY_GATES_ENABLED=true
TYPESCRIPT_ERRORS_ALLOWED=0
ESLINT_ERRORS_ALLOWED=0
TEST_COVERAGE_THRESHOLD=80
PERFORMANCE_BUDGET_MS=2000

# Staging validation settings
VALIDATE_ON_DEPLOY=true
VALIDATE_HEALTH_CHECKS=true
VALIDATE_PERFORMANCE=true
VALIDATE_SECURITY=true
VALIDATE_DATABASE=true

# =============================================================================
# STAGING DEVELOPMENT TOOLS
# =============================================================================
# Development tools enabled in staging
DEV_TOOLS_ENABLED=true
DEBUG_MODE=true
VERBOSE_ERRORS=true
STACK_TRACES=true

# Staging-specific debugging
DEBUG_DATABASE_QUERIES=true
DEBUG_API_CALLS=true
DEBUG_PERFORMANCE=true
DEBUG_SECURITY=true

# =============================================================================
# STAGING ENVIRONMENT VALIDATION
# =============================================================================
# Environment validation settings
VALIDATE_ENV_ON_START=true
REQUIRED_ENV_VARS=DATABASE_URL,JWT_SECRET,DATA_ENCRYPTION_KEY
STAGING_HEALTH_CHECK_URL=/api/health/staging

# Staging readiness checks
CHECK_DATABASE_CONNECTION=true
CHECK_EXTERNAL_SERVICES=true
CHECK_FILE_PERMISSIONS=true
CHECK_MEMORY_LIMITS=true

# =============================================================================
# STAGING METADATA
# =============================================================================
# Staging environment metadata
STAGING_VERSION=2.0
STAGING_DEPLOYMENT_DATE=2025-07-02
STAGING_LAST_UPDATED=2025-07-02T00:00:00Z
STAGING_MAINTAINER=RK Institute DevOps Team

# Environment identification
ENVIRONMENT_NAME=staging
ENVIRONMENT_TYPE=pre-production
ENVIRONMENT_PURPOSE=comprehensive-testing-validation
