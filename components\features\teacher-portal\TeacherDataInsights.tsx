/**
 * Teacher Data Insights Component
 *
 * Handles all data fetching, state management, and real-time updates
 * for the Teacher Portal. Provides clean separation of business logic
 * from presentation components.
 *
 * Design Features:
 * - Real-time data fetching with authentication
 * - Comprehensive error handling
 * - Loading state management
 * - SSR-safe implementation
 * - Clean callback-based state updates
 */

'use client';

import { useEffect } from 'react';

import {
  TeacherDataInsightsProps,
  User,
  TeacherProfile,
  DashboardStats,
} from './types';

export default function TeacherDataInsights({
  onUserUpdate,
  onTeacherProfileUpdate,
  onStatsUpdate,
  onLoadingChange,
}: TeacherDataInsightsProps) {
  useEffect(() => {
    // Initial data fetch
    fetchTeacherData();
  }, []);

  const fetchTeacherData = async () => {
    try {
      onLoadingChange(true);

      // Fetch user data with error handling
      let userData: { success: boolean; user?: any } = { success: false };
      try {
        const userResponse = await fetch('/api/auth/me');
        if (userResponse.ok) {
          userData = await userResponse.json();
        }
      } catch (userError) {
        console.warn('Auth API not available, using fallback data');
      }

      if (userData.success && userData.user) {
        onUserUpdate(userData.user);

        // Fetch teacher profile with error handling
        try {
          const profileResponse = await fetch('/api/teachers/profile');
          if (profileResponse.ok) {
            const profileData = await profileResponse.json();
            if (profileData.success) {
              onTeacherProfileUpdate(profileData.teacher);
            }
          }
        } catch (profileError) {
          console.warn('Teacher profile API not available');
        }

        // Fetch dashboard stats
        await fetchDashboardStats();
      } else {
        // Set fallback data to ensure navigation works
        onUserUpdate({
          id: 'teacher-fallback',
          name: 'Teacher',
          email: '<EMAIL>',
          role: 'TEACHER',
        });
        onTeacherProfileUpdate(null);
        onStatsUpdate({
          totalStudents: 0,
          totalCourses: 0,
          totalLogs: 0,
          achievements: 0,
          concerns: 0,
          progressReports: 0,
        });
      }
    } catch (err) {
      console.error('Error fetching teacher data:', err);
      // Ensure fallback data is set for navigation to work
      onUserUpdate({
        id: 'teacher-fallback',
        name: 'Teacher',
        email: '<EMAIL>',
        role: 'TEACHER',
      });
      onTeacherProfileUpdate(null);
      onStatsUpdate({
        totalStudents: 0,
        totalCourses: 0,
        totalLogs: 0,
        achievements: 0,
        concerns: 0,
        progressReports: 0,
      });
    } finally {
      // Always set loading to false to ensure navigation works
      onLoadingChange(false);
    }
  };

  const fetchDashboardStats = async () => {
    try {
      const token = localStorage.getItem('token');

      let totalStudents = 0;
      let totalCourses = 0;
      let totalLogs = 0;
      let achievements = 0;
      let concerns = 0;
      let progressReports = 0;

      // Fetch students data with error handling
      try {
        const studentsRes = await fetch('/api/students', {
          headers: { Authorization: `Bearer ${token}` },
        });
        if (studentsRes.ok) {
          const studentsData = await studentsRes.json();
          totalStudents = studentsData.length;
        }
      } catch (studentsError) {
        console.warn('Students API not available');
      }

      // Fetch courses data with error handling
      try {
        const coursesRes = await fetch('/api/courses', {
          headers: { Authorization: `Bearer ${token}` },
        });
        if (coursesRes.ok) {
          const coursesData = await coursesRes.json();
          totalCourses = coursesData.length;
        }
      } catch (coursesError) {
        console.warn('Courses API not available');
      }

      // Fetch academic logs with error handling
      try {
        const logsRes = await fetch('/api/academic-logs', {
          headers: { Authorization: `Bearer ${token}` },
        });
        if (logsRes.ok) {
          const logsData = await logsRes.json();
          totalLogs = logsData.length;
          achievements = logsData.filter(
            (log: any) => log.logType === 'ACHIEVEMENT',
          ).length;
          concerns = logsData.filter(
            (log: any) => log.logType === 'CONCERN',
          ).length;
          progressReports = logsData.filter(
            (log: any) => log.logType === 'PROGRESS',
          ).length;
        }
      } catch (logsError) {
        console.warn('Academic logs API not available');
      }

      onStatsUpdate({
        totalStudents,
        totalCourses,
        totalLogs,
        achievements,
        concerns,
        progressReports,
      });
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      // Provide default stats if API fails
      onStatsUpdate({
        totalStudents: 0,
        totalCourses: 0,
        totalLogs: 0,
        achievements: 0,
        concerns: 0,
        progressReports: 0,
      });
    }
  };

  // This component only handles data fetching and doesn't render anything
  return null;
}
