# =============================================================================
# RK Institute Management System - Production .gitignore
# =============================================================================

# =============================================================================
# SECURITY - NEVER COMMIT THESE FILES
# =============================================================================
# Environment files with sensitive data
.env
.env.local
.env.production
.env.production.local
.env.development
.env.development.local
.env.test
.env.test.local

# SSL certificates and keys
*.pem
*.key
*.crt
*.p12
*.pfx
ssl/
certs/

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
backups/
*.sql
*.dump

# Log files
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# =============================================================================
# NODE.JS
# =============================================================================
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
.bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next/
out/

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================
# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# =============================================================================
# DOCKER
# =============================================================================
# Docker files (keep only production ones)
Dockerfile.dev
docker-compose.dev.yml
docker-compose.override.yml

# =============================================================================
# TESTING
# =============================================================================
# Test files and coverage
test/
tests/
__tests__/
*.test.js
*.test.ts
*.test.tsx
*.spec.js
*.spec.ts
*.spec.tsx
jest.config.js
jest.setup.js
coverage/

# =============================================================================
# BUILD ARTIFACTS
# =============================================================================
# Build directories
build/
dist/
.build/

# =============================================================================
# UPLOADS AND USER DATA
# =============================================================================
# User uploaded files (configure based on your needs)
uploads/
user-data/
attachments/

# =============================================================================
# MONITORING AND ANALYTICS
# =============================================================================
# Analytics and monitoring files
.vercel
.netlify

# =============================================================================
# DOCUMENTATION (DEVELOPMENT)
# =============================================================================
# Development documentation
docs/development/
DEVELOPMENT.md
TODO.md
NOTES.md

# =============================================================================
# SCRIPTS (DEVELOPMENT)
# =============================================================================
# Development scripts
scripts/dev/
scripts/test/
scripts/seed/

# =============================================================================
# CONFIGURATION (DEVELOPMENT)
# =============================================================================
# Development configuration files
.eslintrc.*
.prettierrc.*
# tsconfig.json - NEEDED for production deployment on Vercel
tailwind.config.js
postcss.config.js

# =============================================================================
# KEEP THESE FILES (PRODUCTION READY)
# =============================================================================
# These files should be included in production:
# - package.json
# - package-lock.json
# - next.config.js
# - prisma/schema.prisma
# - Dockerfile
# - docker-compose.yml
# - .env.example
# - README.md
# - DEPLOYMENT-GUIDE.md
# - SECURITY.md
# - API-DOCUMENTATION.md

# =============================================================================
# CUSTOM EXCLUSIONS
# =============================================================================
# Add any custom files or directories to exclude
# Example:
# custom-config/
# private-docs/
