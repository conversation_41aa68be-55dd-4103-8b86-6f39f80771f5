{"name": "rk-institute-management-system", "version": "2.0.0", "description": "RK Institute Management System - Production Optimized v2.0", "private": true, "scripts": {"build": "prisma migrate deploy && prisma generate && next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "postinstall": "prisma generate", "db:migrate": "prisma migrate deploy", "db:generate": "prisma generate", "health": "node scripts/health-check.js", "db:seed": "tsx prisma/seed.ts", "test": "vitest", "test:run": "vitest run", "test:watch": "vitest --watch", "test:ui": "vitest --ui --open", "test:coverage": "vitest run --coverage", "test:ci": "vitest run --coverage --reporter=verbose --reporter=junit", "test:changed": "vitest related", "test:debug": "vitest --inspect-brk --no-coverage --single-thread", "test:hooks": "vitest run __tests__/hooks", "test:components": "vitest run __tests__/components", "test:services": "vitest run __tests__/services", "test:integration": "vitest run __tests__/integration", "docs:chronological": "node scripts/generate-chronological-index.js", "docs:update-dates": "node scripts/generate-chronological-index.js", "test:performance": "vitest run --reporter=verbose --pool=forks", "test:migrate": "node scripts/migrate-tests.js", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "quality:check": "npm run type-check && npm run lint && npm run format:check", "quality:fix": "npm run lint:fix && npm run format", "quality:gates": "node scripts/quality-gates.js", "quality:gates:fix": "node scripts/quality-gates.js --fix", "quality:gates:strict": "node scripts/quality-gates.js --strict", "type-check": "tsc --noEmit", "pre-commit": "npm run quality:check && npm run test:run", "staging:validate": "node scripts/staging-validation.js", "staging:report": "node scripts/generate-staging-report.js", "staging:deploy": "vercel --env=staging", "staging:test": "npm run test:run && npm run staging:validate", "staging:full": "npm run quality:gates && npm run test:run && npm run build && npm run staging:deploy", "db:seed:staging": "NODE_ENV=staging tsx prisma/seed.ts", "quality:gates:staging": "NODE_ENV=staging node scripts/quality-gates.js", "validate:staging:comprehensive": "npm run quality:gates:staging && npm run test:run && npm run staging:validate", "production:validate": "node scripts/production-validation.js", "production:rollback": "node scripts/automated-rollback.js", "production:monitor": "node monitoring/scripts/health-check-monitor.js", "production:setup-monitoring": "node scripts/setup-monitoring-dashboard.js --env=production", "production:health": "npm run production:validate", "production:deploy": "npm run quality:gates && npm run test:run && npm run build", "production:full-validation": "npm run production:validate && npm run production:monitor", "monitoring:setup": "node scripts/setup-monitoring-dashboard.js", "monitoring:start": "node monitoring/scripts/health-check-monitor.js"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix --max-warnings 0", "prettier --write"], "*.{js,jsx}": ["eslint --fix --max-warnings 0", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "dependencies": {"@heroicons/react": "^2.2.0", "@modelcontextprotocol/sdk": "^1.13.2", "@modelcontextprotocol/server-filesystem": "^2025.3.28", "@modelcontextprotocol/server-slack": "^2025.4.25", "@next/bundle-analyzer": "^15.3.4", "@octokit/rest": "^22.0.0", "@prisma/client": "^6.9.0", "@ramidecodes/mcp-server-notion": "^1.0.6", "@sentry/mcp-server": "^0.12.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.5", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@vercel/mcp-adapter": "^0.11.1", "autoprefixer": "^10.0.1", "bcryptjs": "^2.4.3", "clsx": "^2.1.1", "cors": "^2.8.5", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "next": "14.0.4", "node-cron": "^4.1.0", "node-fetch": "^3.3.2", "nodemailer": "^7.0.3", "postcss": "^8", "react": "^18.2.0", "react-dom": "^18.2.0", "react-feather": "^2.0.10", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.3.0", "typescript": "^5.3.3", "web-vitals": "^5.0.3", "zod": "^3.22.4"}, "devDependencies": {"@playwright/test": "^1.53.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@vitejs/plugin-react": "^4.3.0", "@vitest/coverage-v8": "^2.1.0", "@vitest/ui": "^2.1.0", "eslint": "^8.55.0", "eslint-config-next": "14.0.4", "jsdom": "^26.1.0", "playwright": "^1.53.1", "prettier": "^3.1.0", "prisma": "^6.9.0", "ts-jest": "^29.4.0", "tsx": "^4.6.2", "vitest": "^2.1.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["institute-management", "education", "fee-management", "student-management", "nextjs", "typescript", "postgresql"], "author": "RK Institute", "license": "PROPRIETARY"}