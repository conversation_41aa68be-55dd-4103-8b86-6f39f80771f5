# =============================================================================
# RK Institute Management System - Production Environment Configuration
# =============================================================================
# 
# SECURITY WARNING: This is a template file. 
# - Copy to .env.production and fill in actual values
# - Never commit .env.production to version control
# - Use Vercel Environment Variables dashboard for production secrets
# 
# Version: 2.0
# Last Updated: 2025-07-02
# =============================================================================

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================
NODE_ENV=production
PORT=3000
NEXT_PUBLIC_APP_URL=https://your-production-domain.com

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Primary database connection (Neon PostgreSQL recommended)
DATABASE_URL=postgresql://username:SECURE_PASSWORD@host:port/database?sslmode=require

# Connection pooling settings
DATABASE_POOL_MIN=5
DATABASE_POOL_MAX=20
DATABASE_TIMEOUT=30000

# Database backup configuration
DATABASE_BACKUP_ENABLED=true
DATABASE_BACKUP_SCHEDULE="0 2 * * *"
DATABASE_BACKUP_RETENTION_DAYS=30

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Secret - Generate with: openssl rand -base64 32
JWT_SECRET=CHANGE_THIS_TO_A_SECURE_32_PLUS_CHARACTER_SECRET_KEY
JWT_EXPIRY=4h

# Data Encryption Key - Generate with: openssl rand -hex 32
DATA_ENCRYPTION_KEY=CHANGE_THIS_TO_A_SECURE_64_CHARACTER_HEX_ENCRYPTION_KEY

# Password Hashing
BCRYPT_ROUNDS=14

# Session Configuration
SESSION_TIMEOUT=14400000
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_TIME=900000

# =============================================================================
# RATE LIMITING & SECURITY
# =============================================================================
# Rate limiting (15 minutes window, 50 requests max)
RATE_LIMITING_ENABLED=true
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=50

# CORS configuration
CORS_ORIGIN=https://your-production-domain.com

# Security features
SECURITY_AUDIT_ENABLED=true
SECURITY_HEADERS_ENABLED=true

# =============================================================================
# MONITORING & LOGGING
# =============================================================================
# Error tracking (Sentry recommended)
SENTRY_DSN=your-sentry-dsn-here
SENTRY_ENVIRONMENT=production

# Logging configuration
LOG_LEVEL=warn
ENABLE_AUDIT_LOGS=true
LOG_RETENTION_DAYS=30

# Health checks
HEALTH_CHECK_URL=/api/health
METRICS_ENABLED=true

# Performance monitoring
FEATURE_PERF_MON=true
FEATURE_ERROR_TRACK=true

# =============================================================================
# FEATURE FLAGS - PRODUCTION
# =============================================================================
# Core features (enabled for production)
FEATURE_REPORTING=true
FEATURE_MOBILE=true
FEATURE_AUDIT=true
FEATURE_RATE_LIMIT=true
FEATURE_INPUT_VALIDATION=true

# Performance features
FEATURE_CACHE=true
FEATURE_LAZY_LOAD=true
FEATURE_IMAGE_OPT=true
FEATURE_DB_OPT=true

# Security features
FEATURE_2FA=false
FEATURE_SECURITY_HEADERS=true

# Advanced features (disabled initially - enable after testing)
FEATURE_REALTIME=false
FEATURE_AI=false
FEATURE_OFFLINE=false

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
# Email service configuration
EMAIL_ENABLED=true
EMAIL_PROVIDER=smtp
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_SECURE=true
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password

# Email templates
EMAIL_FROM_NAME=RK Institute
EMAIL_FROM_ADDRESS=<EMAIL>

# =============================================================================
# FILE STORAGE CONFIGURATION
# =============================================================================
# File upload settings
FILE_UPLOAD_ENABLED=true
FILE_UPLOAD_MAX_SIZE=10485760
FILE_UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,application/pdf

# Storage provider (local, s3, cloudinary)
STORAGE_PROVIDER=local
STORAGE_PATH=/uploads

# AWS S3 Configuration (if using S3)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket

# =============================================================================
# REDIS CONFIGURATION (Optional)
# =============================================================================
# Redis for caching and sessions
REDIS_ENABLED=false
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your-redis-password

# =============================================================================
# BACKUP & RECOVERY
# =============================================================================
# Automated backup configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=/backups

# =============================================================================
# ANALYTICS & TRACKING
# =============================================================================
# Google Analytics
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX

# Application analytics
ANALYTICS_ENABLED=true
TRACK_USER_ACTIONS=true
TRACK_PERFORMANCE=true

# =============================================================================
# NOTIFICATION SERVICES
# =============================================================================
# Slack notifications
SLACK_WEBHOOK_URL=your-slack-webhook-url
SLACK_CHANNEL=#alerts

# SMS notifications (Twilio)
TWILIO_ENABLED=false
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token
TWILIO_PHONE_NUMBER=+**********

# =============================================================================
# API INTEGRATIONS
# =============================================================================
# External API configurations
EXTERNAL_API_ENABLED=false
EXTERNAL_API_BASE_URL=https://api.external-service.com
EXTERNAL_API_KEY=your-external-api-key

# =============================================================================
# DEVELOPMENT & DEBUGGING (Production Settings)
# =============================================================================
# Debug settings (disabled in production)
DEBUG=false
VERBOSE_LOGGING=false
ENABLE_PROFILING=false

# Development tools (disabled in production)
ENABLE_DEVTOOLS=false
ENABLE_MOCK_DATA=false

# =============================================================================
# CRON JOBS & SCHEDULED TASKS
# =============================================================================
# Automated tasks
CRON_ENABLED=true
CRON_BACKUP_SCHEDULE="0 2 * * *"
CRON_CLEANUP_SCHEDULE="0 3 * * 0"
CRON_REPORTS_SCHEDULE="0 8 * * 1"

# =============================================================================
# PERFORMANCE OPTIMIZATION
# =============================================================================
# Caching settings
CACHE_ENABLED=true
CACHE_TTL=3600
CACHE_MAX_SIZE=100

# Image optimization
IMAGE_OPTIMIZATION=true
IMAGE_QUALITY=80
IMAGE_FORMATS=webp,jpg,png

# =============================================================================
# COMPLIANCE & AUDIT
# =============================================================================
# Compliance features
GDPR_COMPLIANCE=true
AUDIT_TRAIL_ENABLED=true
DATA_RETENTION_DAYS=2555

# Privacy settings
PRIVACY_MODE=strict
COOKIE_CONSENT_REQUIRED=true

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================
# Deployment settings
DEPLOYMENT_ENVIRONMENT=production
DEPLOYMENT_VERSION=2.0.0
BUILD_TIMESTAMP=2025-07-02T00:00:00Z

# Health check configuration
HEALTH_CHECK_TIMEOUT=30000
HEALTH_CHECK_RETRIES=3

# =============================================================================
# VERCEL SPECIFIC CONFIGURATION
# =============================================================================
# Vercel deployment settings
VERCEL_ENV=production
VERCEL_URL=your-production-domain.com

# Function configuration
FUNCTION_TIMEOUT=30
FUNCTION_MEMORY=1024

# =============================================================================
# SECURITY HEADERS CONFIGURATION
# =============================================================================
# Content Security Policy
CSP_ENABLED=true
CSP_REPORT_ONLY=false

# HSTS Configuration
HSTS_ENABLED=true
HSTS_MAX_AGE=31536000

# =============================================================================
# END OF CONFIGURATION
# =============================================================================
# 
# IMPORTANT REMINDERS:
# 1. Replace all placeholder values with actual production values
# 2. Generate strong, unique secrets for JWT_SECRET and DATA_ENCRYPTION_KEY
# 3. Configure proper database connection string with SSL
# 4. Set up monitoring and error tracking services
# 5. Configure email and notification services
# 6. Test all configurations in staging before production deployment
# 7. Regularly rotate secrets and passwords
# 8. Monitor logs and metrics for security and performance
# 
# =============================================================================
