'use client';

import React from 'react';

interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: string | React.ReactNode;
  color?: 'blue' | 'green' | 'purple' | 'red' | 'yellow' | 'indigo';
  onClick?: () => void;
}

export default function StatCard({
  title,
  value,
  subtitle,
  icon,
  color = 'blue',
  onClick
}: StatCardProps) {
  const colorClasses = {
    blue: 'bg-blue-100 text-blue-600',
    green: 'bg-green-100 text-green-600',
    purple: 'bg-purple-100 text-purple-600',
    red: 'bg-red-100 text-red-600',
    yellow: 'bg-yellow-100 text-yellow-600',
    indigo: 'bg-indigo-100 text-indigo-600'
  };

  const textColorClasses = {
    blue: 'text-blue-600',
    green: 'text-green-600',
    purple: 'text-purple-600',
    red: 'text-red-600',
    yellow: 'text-yellow-600',
    indigo: 'text-indigo-600'
  };

  return (
    <div
      className={`bg-white p-3 sm:p-6 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow ${
        onClick ? 'cursor-pointer' : ''
      }`}
      onClick={onClick}
    >
      <div className='flex items-center justify-between'>
        <div className='flex-1 min-w-0'>
          <p className='text-xs sm:text-sm font-medium text-gray-600'>
            {title}
          </p>
          <p
            className={`text-xl sm:text-3xl font-bold ${textColorClasses[color]} truncate`}
          >
            {typeof value === 'number' ? value.toLocaleString() : value}
          </p>
          {subtitle && (
            <p className={`text-xs sm:text-sm mt-1 ${textColorClasses[color]}`}>
              {subtitle}
            </p>
          )}
        </div>
        <div
          className={`p-2 sm:p-3 rounded-lg ${colorClasses[color]} ml-2 sm:ml-3 flex-shrink-0`}
        >
          {typeof icon === 'string' ? (
            <span className='text-xl sm:text-3xl'>{icon}</span>
          ) : (
            <div className='flex items-center justify-center'>{icon}</div>
          )}
        </div>
      </div>
    </div>
  );
}
