
> rk-institute-management-system@2.0.0 test
> vitest --run --reporter=json

The CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.
{"numTotalTestSuites":81,"numPassedTestSuites":28,"numFailedTestSuites":53,"numPendingTestSuites":0,"numTotalTests":219,"numPassedTests":144,"numFailedTests":75,"numPendingTests":0,"numTodoTests":0,"snapshot":{"added":0,"failure":false,"filesAdded":0,"filesRemoved":0,"filesRemovedList":[],"filesUnmatched":0,"filesUpdated":0,"matched":0,"total":0,"unchecked":0,"uncheckedKeysByFile":[],"unmatched":0,"updated":0,"didUpdate":false},"startTime":1751345984160,"success":false,"testResults":[{"assertionResults":[{"ancestorTitles":["Phase 1 Implementation Integration Tests","Feature Flags System"],"fullName":"Phase 1 Implementation Integration Tests Feature Flags System should provide consistent feature flag access","status":"passed","title":"should provide consistent feature flag access","duration":8.253299999999854,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Feature Flags System"],"fullName":"Phase 1 Implementation Integration Tests Feature Flags System should return complete feature flags object","status":"passed","title":"should return complete feature flags object","duration":7.380900000000111,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Feature Flags System"],"fullName":"Phase 1 Implementation Integration Tests Feature Flags System should provide analytics data","status":"passed","title":"should provide analytics data","duration":7.215599999999995,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Feature Flags System"],"fullName":"Phase 1 Implementation Integration Tests Feature Flags System should validate feature flag configuration","status":"passed","title":"should validate feature flag configuration","duration":7.028800000000047,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Service Layer Implementation"],"fullName":"Phase 1 Implementation Integration Tests Service Layer Implementation should create StudentService instance","status":"passed","title":"should create StudentService instance","duration":4.822599999999966,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Service Layer Implementation"],"fullName":"Phase 1 Implementation Integration Tests Service Layer Implementation should provide health status","status":"passed","title":"should provide health status","duration":10.007500000000164,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Service Layer Implementation"],"fullName":"Phase 1 Implementation Integration Tests Service Layer Implementation should handle service operations with proper error handling","status":"passed","title":"should handle service operations with proper error handling","duration":8.0949999999998,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Service Layer Implementation"],"fullName":"Phase 1 Implementation Integration Tests Service Layer Implementation should provide consistent service result format","status":"passed","title":"should provide consistent service result format","duration":5.633200000000215,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Service Layer Implementation"],"fullName":"Phase 1 Implementation Integration Tests Service Layer Implementation should handle pagination correctly","status":"failed","title":"should handle pagination correctly","duration":16.71219999999994,"failureMessages":["AssertionError: expected false to be true // Object.is equality\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:202:49\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/vitest/dist/chunks/runBaseTests.9YDrdSI4.js:130:11"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should initialize module registry","status":"passed","title":"should initialize module registry","duration":7.098599999999806,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should register modules successfully","status":"passed","title":"should register modules successfully","duration":8.125700000000052,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should provide module status information","status":"failed","title":"should provide module status information","duration":24.866700000000037,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:222:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should handle module dependencies correctly","status":"failed","title":"should handle module dependencies correctly","duration":7.076100000000224,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:242:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should integrate with feature flags","status":"failed","title":"should integrate with feature flags","duration":13.684999999999945,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:259:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should provide module statistics","status":"failed","title":"should provide module statistics","duration":6.138000000000375,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:272:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","System Integration"],"fullName":"Phase 1 Implementation Integration Tests System Integration should integrate all Phase 1 components","status":"failed","title":"should integrate all Phase 1 components","duration":9.433799999999792,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:295:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","System Integration"],"fullName":"Phase 1 Implementation Integration Tests System Integration should maintain backwards compatibility","status":"failed","title":"should maintain backwards compatibility","duration":9.925000000000182,"failureMessages":["AssertionError: expected [Function] to not throw an error but 'Error: Module core is already registeΓÇª' was thrown\n    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/expect/dist/index.js:1437:21)\n    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/expect/dist/index.js:923:17)\n    at Proxy.methodWrapper (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/chai/chai.js:1618:25)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:323:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","System Integration"],"fullName":"Phase 1 Implementation Integration Tests System Integration should provide consistent error handling across systems","status":"passed","title":"should provide consistent error handling across systems","duration":5.953100000000177,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","System Integration"],"fullName":"Phase 1 Implementation Integration Tests System Integration should provide performance metrics","status":"failed","title":"should provide performance metrics","duration":6.112599999999929,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:345:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Phase 1 Success Criteria Validation"],"fullName":"Phase 1 Implementation Integration Tests Phase 1 Success Criteria Validation should meet all Phase 1 success criteria","status":"failed","title":"should meet all Phase 1 success criteria","duration":16.269399999999678,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:379:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Phase 1 Success Criteria Validation"],"fullName":"Phase 1 Implementation Integration Tests Phase 1 Success Criteria Validation should provide development experience improvements","status":"failed","title":"should provide development experience improvements","duration":5.375100000000202,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:411:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Phase 1 Success Criteria Validation"],"fullName":"Phase 1 Implementation Integration Tests Phase 1 Success Criteria Validation should maintain performance standards","status":"failed","title":"should maintain performance standards","duration":8.057099999999991,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:426:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}}],"startTime":1751345986957,"endTime":1751345987161.0571,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/integration/Phase1Implementation.test.ts"},{"assertionResults":[{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should register all modules without errors","status":"passed","title":"should register all modules without errors","duration":4.218199999999797,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should register core modules first","status":"passed","title":"should register core modules first","duration":1.2752000000000407,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should register feature modules with proper dependencies","status":"passed","title":"should register feature modules with proper dependencies","duration":1.8451000000000022,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should handle feature flag controlled modules","status":"passed","title":"should handle feature flag controlled modules","duration":0.9068999999999505,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should register integration modules conditionally","status":"passed","title":"should register integration modules conditionally","duration":0.8821000000002641,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should register experimental modules conditionally","status":"passed","title":"should register experimental modules conditionally","duration":1.0400999999997111,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should provide comprehensive module status","status":"passed","title":"should provide comprehensive module status","duration":1.4927000000002408,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should have correct module counts","status":"passed","title":"should have correct module counts","duration":0.7118000000000393,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should categorize modules correctly","status":"passed","title":"should categorize modules correctly","duration":0.7056999999999789,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should track module status correctly","status":"passed","title":"should track module status correctly","duration":0.7768000000000939,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should identify enabled core modules","status":"passed","title":"should identify enabled core modules","duration":0.7809999999999491,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should identify disabled feature-controlled modules","status":"passed","title":"should identify disabled feature-controlled modules","duration":0.6493000000000393,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Dependencies and Relationships"],"fullName":"Module Registration System Module Dependencies and Relationships should maintain proper dependency hierarchy","status":"passed","title":"should maintain proper dependency hierarchy","duration":1.1183000000000902,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Dependencies and Relationships"],"fullName":"Module Registration System Module Dependencies and Relationships should track module dependents correctly","status":"passed","title":"should track module dependents correctly","duration":0.7348999999999251,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Dependencies and Relationships"],"fullName":"Module Registration System Module Dependencies and Relationships should prevent disabling modules with active dependents","status":"passed","title":"should prevent disabling modules with active dependents","duration":0.5945999999999003,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Feature Flag Integration"],"fullName":"Module Registration System Feature Flag Integration should respect feature flag states during registration","status":"failed","title":"should respect feature flag states during registration","duration":16.254599999999755,"failureMessages":["AssertionError: expected true to be false // Object.is equality\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\modules\\ModuleRegistration.test.ts:254:53\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/vitest/dist/chunks/runBaseTests.9YDrdSI4.js:130:11"],"meta":{}},{"ancestorTitles":["Module Registration System","Feature Flag Integration"],"fullName":"Module Registration System Feature Flag Integration should handle modules with mixed feature requirements","status":"passed","title":"should handle modules with mixed feature requirements","duration":1.071599999999762,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Performance and Scalability"],"fullName":"Module Registration System Performance and Scalability should register modules efficiently","status":"passed","title":"should register modules efficiently","duration":0.8033999999997832,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Performance and Scalability"],"fullName":"Module Registration System Performance and Scalability should provide status information efficiently","status":"passed","title":"should provide status information efficiently","duration":1.0032000000001062,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Performance and Scalability"],"fullName":"Module Registration System Performance and Scalability should handle module queries efficiently","status":"passed","title":"should handle module queries efficiently","duration":2.456500000000233,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Error Handling and Resilience"],"fullName":"Module Registration System Error Handling and Resilience should handle registration errors gracefully","status":"passed","title":"should handle registration errors gracefully","duration":1.8532999999997628,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Error Handling and Resilience"],"fullName":"Module Registration System Error Handling and Resilience should provide error information in status","status":"passed","title":"should provide error information in status","duration":0.9465000000000146,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Error Handling and Resilience"],"fullName":"Module Registration System Error Handling and Resilience should handle missing dependencies gracefully","status":"passed","title":"should handle missing dependencies gracefully","duration":0.6755999999995765,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Health and Monitoring"],"fullName":"Module Registration System Module Health and Monitoring should provide health status for modules","status":"passed","title":"should provide health status for modules","duration":2.9859999999998763,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Health and Monitoring"],"fullName":"Module Registration System Module Health and Monitoring should track module performance metrics","status":"passed","title":"should track module performance metrics","duration":1.272599999999784,"failureMessages":[],"meta":{}}],"startTime":1751345986944,"endTime":1751345986993.2727,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/modules/ModuleRegistration.test.ts"},{"assertionResults":[{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should register a valid module successfully","status":"passed","title":"should register a valid module successfully","duration":3.8391999999998916,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should validate module configuration","status":"passed","title":"should validate module configuration","duration":0.7219999999997526,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should prevent duplicate module registration","status":"passed","title":"should prevent duplicate module registration","duration":0.46270000000004075,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should validate dependencies exist","status":"passed","title":"should validate dependencies exist","duration":0.3838999999998123,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should detect circular dependencies","status":"failed","title":"should detect circular dependencies","duration":9.165600000000268,"failureMessages":["Error: Dependency module-b not found for module module-a\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:202:17)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\modules\\ModuleRegistry.test.ts:148:16\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should handle feature flag requirements","status":"passed","title":"should handle feature flag requirements","duration":4.158399999999801,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should set module metadata correctly","status":"passed","title":"should set module metadata correctly","duration":0.7372999999997774,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should check if module is enabled","status":"passed","title":"should check if module is enabled","duration":0.6302000000000589,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should get all modules","status":"passed","title":"should get all modules","duration":1.4522999999999229,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should get enabled modules only","status":"passed","title":"should get enabled modules only","duration":0.6622999999999593,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should get modules by category","status":"passed","title":"should get modules by category","duration":0.7177999999998974,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should get module dependencies","status":"passed","title":"should get module dependencies","duration":1.5106999999998152,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should get module dependents","status":"passed","title":"should get module dependents","duration":1.0585999999998421,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should track access metrics","status":"passed","title":"should track access metrics","duration":0.5850999999997839,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should check if module can be disabled","status":"passed","title":"should check if module can be disabled","duration":0.3771999999999025,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should disable module without dependents","status":"passed","title":"should disable module without dependents","duration":0.5256999999996879,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should prevent disabling module with dependents","status":"passed","title":"should prevent disabling module with dependents","duration":0.34489999999959764,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should enable disabled module","status":"passed","title":"should enable disabled module","duration":0.47100000000000364,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should validate dependencies when enabling","status":"passed","title":"should validate dependencies when enabling","duration":0.399199999999837,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should validate feature flags when enabling","status":"passed","title":"should validate feature flags when enabling","duration":0.604200000000219,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Event System"],"fullName":"ModuleRegistry Event System should emit registration events","status":"passed","title":"should emit registration events","duration":2.4634000000000924,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Event System"],"fullName":"ModuleRegistry Event System should emit enable/disable events","status":"passed","title":"should emit enable/disable events","duration":1.587500000000091,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Event System"],"fullName":"ModuleRegistry Event System should handle event listener errors gracefully","status":"passed","title":"should handle event listener errors gracefully","duration":0.9906999999998334,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Event System"],"fullName":"ModuleRegistry Event System should remove event listeners","status":"passed","title":"should remove event listeners","duration":0.9504000000001724,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Health Monitoring"],"fullName":"ModuleRegistry Health Monitoring should perform health checks on all modules","status":"passed","title":"should perform health checks on all modules","duration":2.012000000000171,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Health Monitoring"],"fullName":"ModuleRegistry Health Monitoring should emit health check events","status":"passed","title":"should emit health check events","duration":1.4030000000002474,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Health Monitoring"],"fullName":"ModuleRegistry Health Monitoring should detect dependency health issues","status":"passed","title":"should detect dependency health issues","duration":1.7276999999999134,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Statistics and Performance"],"fullName":"ModuleRegistry Statistics and Performance should provide comprehensive statistics","status":"passed","title":"should provide comprehensive statistics","duration":1.817199999999957,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Statistics and Performance"],"fullName":"ModuleRegistry Statistics and Performance should track memory usage estimates","status":"passed","title":"should track memory usage estimates","duration":1.0354000000002088,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Statistics and Performance"],"fullName":"ModuleRegistry Statistics and Performance should track load time performance","status":"passed","title":"should track load time performance","duration":0.9094000000000051,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Error Handling and Edge Cases"],"fullName":"ModuleRegistry Error Handling and Edge Cases should handle invalid module configurations gracefully","status":"passed","title":"should handle invalid module configurations gracefully","duration":1.8890000000001237,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Error Handling and Edge Cases"],"fullName":"ModuleRegistry Error Handling and Edge Cases should handle non-existent module operations","status":"passed","title":"should handle non-existent module operations","duration":1.1335999999996602,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Error Handling and Edge Cases"],"fullName":"ModuleRegistry Error Handling and Edge Cases should handle complex dependency chains","status":"passed","title":"should handle complex dependency chains","duration":1.1417999999998756,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Error Handling and Edge Cases"],"fullName":"ModuleRegistry Error Handling and Edge Cases should handle module registration errors","status":"passed","title":"should handle module registration errors","duration":1.064900000000307,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Integration with Feature Flags"],"fullName":"ModuleRegistry Integration with Feature Flags should respect feature flag changes","status":"failed","title":"should respect feature flag changes","duration":7.528700000000299,"failureMessages":["TypeError: isFeatureEnabled.mockReturnValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\modules\\ModuleRegistry.test.ts:524:24\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/vitest/dist/chunks/runBaseTests.9YDrdSI4.js:130:11"],"meta":{}},{"ancestorTitles":["ModuleRegistry","Integration with Feature Flags"],"fullName":"ModuleRegistry Integration with Feature Flags should handle optional features correctly","status":"passed","title":"should handle optional features correctly","duration":5.266999999999825,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Performance Benchmarks"],"fullName":"ModuleRegistry Performance Benchmarks should register modules efficiently","status":"passed","title":"should register modules efficiently","duration":2.144800000000032,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Performance Benchmarks"],"fullName":"ModuleRegistry Performance Benchmarks should query modules efficiently","status":"passed","title":"should query modules efficiently","duration":2.1894999999999527,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Performance Benchmarks"],"fullName":"ModuleRegistry Performance Benchmarks should handle health checks efficiently","status":"passed","title":"should handle health checks efficiently","duration":0.8663000000001375,"failureMessages":[],"meta":{}}],"startTime":1751345986944,"endTime":1751345987012.8662,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/modules/ModuleRegistry.test.ts"},{"assertionResults":[{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Feature Flags System"],"fullName":"Phase 1 Implementation - Simple Validation Feature Flags System should import feature flags module without errors","status":"passed","title":"should import feature flags module without errors","duration":50.63680000000022,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Feature Flags System"],"fullName":"Phase 1 Implementation - Simple Validation Feature Flags System should provide isFeatureEnabled function","status":"passed","title":"should provide isFeatureEnabled function","duration":11.040500000000065,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Feature Flags System"],"fullName":"Phase 1 Implementation - Simple Validation Feature Flags System should provide getAllFeatureFlags function","status":"passed","title":"should provide getAllFeatureFlags function","duration":7.1098000000001775,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Feature Flags System"],"fullName":"Phase 1 Implementation - Simple Validation Feature Flags System should return boolean values for feature flags","status":"passed","title":"should return boolean values for feature flags","duration":7.302000000000135,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Feature Flags System"],"fullName":"Phase 1 Implementation - Simple Validation Feature Flags System should return complete feature flags object","status":"passed","title":"should return complete feature flags object","duration":8.213799999999992,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Feature Flags System"],"fullName":"Phase 1 Implementation - Simple Validation Feature Flags System should provide analytics functionality","status":"passed","title":"should provide analytics functionality","duration":8.750299999999697,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Service Layer Implementation"],"fullName":"Phase 1 Implementation - Simple Validation Service Layer Implementation should import BaseService without errors","status":"passed","title":"should import BaseService without errors","duration":7.180299999999988,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Service Layer Implementation"],"fullName":"Phase 1 Implementation - Simple Validation Service Layer Implementation should import StudentService without errors","status":"passed","title":"should import StudentService without errors","duration":7.710799999999836,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Service Layer Implementation"],"fullName":"Phase 1 Implementation - Simple Validation Service Layer Implementation should provide BaseService class","status":"passed","title":"should provide BaseService class","duration":11.659899999999652,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Service Layer Implementation"],"fullName":"Phase 1 Implementation - Simple Validation Service Layer Implementation should provide StudentService class","status":"passed","title":"should provide StudentService class","duration":9.484000000000378,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Service Layer Implementation"],"fullName":"Phase 1 Implementation - Simple Validation Service Layer Implementation should create StudentService instance","status":"passed","title":"should create StudentService instance","duration":24.052700000000186,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Service Layer Implementation"],"fullName":"Phase 1 Implementation - Simple Validation Service Layer Implementation should provide required service methods","status":"passed","title":"should provide required service methods","duration":22.58129999999983,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Service Layer Implementation"],"fullName":"Phase 1 Implementation - Simple Validation Service Layer Implementation should provide ServiceResult interface types","status":"passed","title":"should provide ServiceResult interface types","duration":8.26240000000007,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should import ModuleRegistry without errors","status":"passed","title":"should import ModuleRegistry without errors","duration":7.757599999999911,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should import module registration without errors","status":"passed","title":"should import module registration without errors","duration":12.73279999999977,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should provide ModuleRegistry class","status":"passed","title":"should provide ModuleRegistry class","duration":11.866699999999582,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should provide global moduleRegistry instance","status":"passed","title":"should provide global moduleRegistry instance","duration":8.857700000000023,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should provide module registration functions","status":"passed","title":"should provide module registration functions","duration":9.733299999999872,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should register modules successfully","status":"passed","title":"should register modules successfully","duration":12.601200000000063,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should provide module status information","status":"passed","title":"should provide module status information","duration":17.086499999999887,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should handle module dependencies","status":"passed","title":"should handle module dependencies","duration":11.956499999999778,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should provide module statistics","status":"passed","title":"should provide module statistics","duration":9.537300000000414,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","System Integration"],"fullName":"Phase 1 Implementation - Simple Validation System Integration should integrate all Phase 1 components without errors","status":"passed","title":"should integrate all Phase 1 components without errors","duration":8.182700000000295,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","System Integration"],"fullName":"Phase 1 Implementation - Simple Validation System Integration should maintain backwards compatibility","status":"passed","title":"should maintain backwards compatibility","duration":5.119899999999689,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","System Integration"],"fullName":"Phase 1 Implementation - Simple Validation System Integration should provide consistent interfaces","status":"passed","title":"should provide consistent interfaces","duration":46.60739999999987,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Phase 1 Success Criteria"],"fullName":"Phase 1 Implementation - Simple Validation Phase 1 Success Criteria should meet all Phase 1 success criteria","status":"failed","title":"should meet all Phase 1 success criteria","duration":35.86720000000014,"failureMessages":["AssertionError: expected [Function] to not throw an error but 'Error: Cannot find module \\'@/lib/conΓÇª' was thrown\n    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/expect/dist/index.js:1437:21)\n    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/expect/dist/index.js:923:17)\n    at Proxy.methodWrapper (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/chai/chai.js:1618:25)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\simple\\Phase1Validation.test.ts:311:14\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Phase 1 Success Criteria"],"fullName":"Phase 1 Implementation - Simple Validation Phase 1 Success Criteria should provide development experience improvements","status":"passed","title":"should provide development experience improvements","duration":17.35279999999966,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Phase 1 Success Criteria"],"fullName":"Phase 1 Implementation - Simple Validation Phase 1 Success Criteria should maintain performance standards","status":"passed","title":"should maintain performance standards","duration":13.086699999999837,"failureMessages":[],"meta":{}}],"startTime":1751345986895,"endTime":1751345987309.0867,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/simple/Phase1Validation.test.ts"},{"assertionResults":[{"ancestorTitles":["BaseService","Initialization and Connection Management"],"fullName":"BaseService Initialization and Connection Management should initialize Prisma client successfully","status":"failed","title":"should initialize Prisma client successfully","duration":56.78009999999995,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Initialization and Connection Management"],"fullName":"BaseService Initialization and Connection Management should handle database connection failure","status":"failed","title":"should handle database connection failure","duration":7.226799999999912,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Initialization and Connection Management"],"fullName":"BaseService Initialization and Connection Management should reuse existing Prisma client instance","status":"failed","title":"should reuse existing Prisma client instance","duration":7.01279999999997,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Error Handling"],"fullName":"BaseService Error Handling should handle unique constraint violation (P2002)","status":"failed","title":"should handle unique constraint violation (P2002)","duration":15.122400000000198,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Error Handling"],"fullName":"BaseService Error Handling should handle record not found (P2025)","status":"failed","title":"should handle record not found (P2025)","duration":6.045599999999922,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Error Handling"],"fullName":"BaseService Error Handling should handle foreign key constraint violation (P2003)","status":"failed","title":"should handle foreign key constraint violation (P2003)","duration":8.375299999999697,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Error Handling"],"fullName":"BaseService Error Handling should handle generic errors","status":"failed","title":"should handle generic errors","duration":12.472999999999956,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Error Handling"],"fullName":"BaseService Error Handling should include context in error metadata","status":"failed","title":"should include context in error metadata","duration":7.561500000000251,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Success Response Handling"],"fullName":"BaseService Success Response Handling should create successful response with data","status":"failed","title":"should create successful response with data","duration":9.15960000000041,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Success Response Handling"],"fullName":"BaseService Success Response Handling should include metadata in successful responses","status":"failed","title":"should include metadata in successful responses","duration":6.514299999999821,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Pagination Utilities"],"fullName":"BaseService Pagination Utilities should validate and apply default pagination options","status":"failed","title":"should validate and apply default pagination options","duration":6.962700000000041,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Pagination Utilities"],"fullName":"BaseService Pagination Utilities should validate pagination options with custom values","status":"failed","title":"should validate pagination options with custom values","duration":5.75189999999975,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Pagination Utilities"],"fullName":"BaseService Pagination Utilities should enforce maximum limit of 100","status":"failed","title":"should enforce maximum limit of 100","duration":7.60109999999986,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Pagination Utilities"],"fullName":"BaseService Pagination Utilities should enforce minimum page of 1","status":"failed","title":"should enforce minimum page of 1","duration":12.969700000000103,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Pagination Utilities"],"fullName":"BaseService Pagination Utilities should build correct pagination metadata","status":"failed","title":"should build correct pagination metadata","duration":7.34920000000011,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Input Sanitization"],"fullName":"BaseService Input Sanitization should sanitize string inputs","status":"failed","title":"should sanitize string inputs","duration":8.791999999999916,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Input Sanitization"],"fullName":"BaseService Input Sanitization should sanitize object inputs recursively","status":"failed","title":"should sanitize object inputs recursively","duration":7.330600000000231,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Input Sanitization"],"fullName":"BaseService Input Sanitization should filter out dangerous keys","status":"failed","title":"should filter out dangerous keys","duration":10.134999999999764,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Field Validation"],"fullName":"BaseService Field Validation should validate required fields","status":"failed","title":"should validate required fields","duration":6.547199999999975,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Health Checks"],"fullName":"BaseService Health Checks should return healthy status when database is available","status":"failed","title":"should return healthy status when database is available","duration":41.03619999999955,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Health Checks"],"fullName":"BaseService Health Checks should return unhealthy status when database is unavailable","status":"failed","title":"should return unhealthy status when database is unavailable","duration":6.13379999999961,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Performance Monitoring"],"fullName":"BaseService Performance Monitoring should track operation duration","status":"failed","title":"should track operation duration","duration":6.241399999999885,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Performance Monitoring"],"fullName":"BaseService Performance Monitoring should include timestamp in metadata","status":"failed","title":"should include timestamp in metadata","duration":6.481099999999969,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Transaction Handling"],"fullName":"BaseService Transaction Handling should execute operations within transactions","status":"failed","title":"should execute operations within transactions","duration":6.102200000000266,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Transaction Handling"],"fullName":"BaseService Transaction Handling should handle transaction failures","status":"failed","title":"should handle transaction failures","duration":6.421299999999974,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Transaction Handling"],"fullName":"BaseService Transaction Handling should support custom transaction options","status":"failed","title":"should support custom transaction options","duration":6.403699999999844,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Cleanup and Disconnection"],"fullName":"BaseService Cleanup and Disconnection should disconnect from database","status":"failed","title":"should disconnect from database","duration":10.439400000000205,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Cleanup and Disconnection"],"fullName":"BaseService Cleanup and Disconnection should handle disconnect errors gracefully","status":"failed","title":"should handle disconnect errors gracefully","duration":5.340900000000147,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}}],"startTime":1751345986985,"endTime":1751345987292.3408,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/services/BaseService.test.ts"},{"assertionResults":[{"ancestorTitles":["StudentService","Service Initialization"],"fullName":"StudentService Service Initialization should create StudentService instance","status":"passed","title":"should create StudentService instance","duration":6.3706999999999425,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Service Initialization"],"fullName":"StudentService Service Initialization should inherit from BaseService","status":"passed","title":"should inherit from BaseService","duration":1.8521999999998116,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Create Student Operations"],"fullName":"StudentService Create Student Operations should create student successfully with valid data","status":"passed","title":"should create student successfully with valid data","duration":8.483400000000074,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Create Student Operations"],"fullName":"StudentService Create Student Operations should validate required fields","status":"passed","title":"should validate required fields","duration":2.8728000000000975,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Create Student Operations"],"fullName":"StudentService Create Student Operations should validate family exists","status":"passed","title":"should validate family exists","duration":2.275499999999738,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Create Student Operations"],"fullName":"StudentService Create Student Operations should check for duplicate student ID","status":"passed","title":"should check for duplicate student ID","duration":3.610099999999875,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Create Student Operations"],"fullName":"StudentService Create Student Operations should handle database errors during creation","status":"passed","title":"should handle database errors during creation","duration":3.480000000000018,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Create Student Operations"],"fullName":"StudentService Create Student Operations should set default enrollment date if not provided","status":"passed","title":"should set default enrollment date if not provided","duration":3.9967000000001462,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Find Student Operations"],"fullName":"StudentService Find Student Operations should find student by ID successfully","status":"passed","title":"should find student by ID successfully","duration":4.13169999999991,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Find Student Operations"],"fullName":"StudentService Find Student Operations should handle student not found","status":"passed","title":"should handle student not found","duration":2.2269999999998618,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Find Student Operations"],"fullName":"StudentService Find Student Operations should validate student ID format","status":"passed","title":"should validate student ID format","duration":1.5041999999998552,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Find Student Operations"],"fullName":"StudentService Find Student Operations should find students by family ID","status":"passed","title":"should find students by family ID","duration":2.3846999999996115,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Update Student Operations"],"fullName":"StudentService Update Student Operations should update student successfully","status":"passed","title":"should update student successfully","duration":2.0657000000001062,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Update Student Operations"],"fullName":"StudentService Update Student Operations should validate student exists before update","status":"passed","title":"should validate student exists before update","duration":2.4972999999999956,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Update Student Operations"],"fullName":"StudentService Update Student Operations should check for duplicate student ID during update","status":"failed","title":"should check for duplicate student ID during update","duration":15.629899999999907,"failureMessages":["AssertionError: expected true to be false // Object.is equality\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\StudentService.test.ts:263:30\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/vitest/dist/chunks/runBaseTests.9YDrdSI4.js:130:11"],"meta":{}},{"ancestorTitles":["StudentService","Update Student Operations"],"fullName":"StudentService Update Student Operations should allow updating to same student ID","status":"passed","title":"should allow updating to same student ID","duration":4.0098000000002685,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Delete Student Operations"],"fullName":"StudentService Delete Student Operations should soft delete student successfully","status":"passed","title":"should soft delete student successfully","duration":4.542200000000321,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Delete Student Operations"],"fullName":"StudentService Delete Student Operations should validate student exists before deletion","status":"passed","title":"should validate student exists before deletion","duration":3.2038000000002285,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Delete Student Operations"],"fullName":"StudentService Delete Student Operations should prevent deletion of already inactive student","status":"passed","title":"should prevent deletion of already inactive student","duration":5.192399999999907,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Search and Pagination"],"fullName":"StudentService Search and Pagination should find many students with default pagination","status":"passed","title":"should find many students with default pagination","duration":7.4770000000003165,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Search and Pagination"],"fullName":"StudentService Search and Pagination should apply search filters correctly","status":"passed","title":"should apply search filters correctly","duration":4.171800000000076,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Search and Pagination"],"fullName":"StudentService Search and Pagination should handle date range filters","status":"passed","title":"should handle date range filters","duration":9.014599999999973,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Search and Pagination"],"fullName":"StudentService Search and Pagination should include inactive students when requested","status":"passed","title":"should include inactive students when requested","duration":3.9708999999998014,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Statistics and Analytics"],"fullName":"StudentService Statistics and Analytics should get student statistics","status":"passed","title":"should get student statistics","duration":5.606200000000172,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Statistics and Analytics"],"fullName":"StudentService Statistics and Analytics should handle empty statistics gracefully","status":"passed","title":"should handle empty statistics gracefully","duration":5.1197000000001935,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Performance Testing"],"fullName":"StudentService Performance Testing should handle large datasets efficiently","status":"failed","title":"should handle large datasets efficiently","duration":6.5383999999999105,"failureMessages":["AssertionError: expected 1000 to be less than or equal to 50\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\StudentService.test.ts:480:41\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/vitest/dist/chunks/runBaseTests.9YDrdSI4.js:130:11"],"meta":{}},{"ancestorTitles":["StudentService","Performance Testing"],"fullName":"StudentService Performance Testing should track operation performance metrics","status":"passed","title":"should track operation performance metrics","duration":5.7973000000001775,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Performance Testing"],"fullName":"StudentService Performance Testing should handle concurrent operations","status":"passed","title":"should handle concurrent operations","duration":8.916400000000067,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Input Validation and Edge Cases"],"fullName":"StudentService Input Validation and Edge Cases should handle invalid input data gracefully","status":"passed","title":"should handle invalid input data gracefully","duration":2.9506000000001222,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Input Validation and Edge Cases"],"fullName":"StudentService Input Validation and Edge Cases should handle edge case data","status":"passed","title":"should handle edge case data","duration":3.7352999999998246,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Input Validation and Edge Cases"],"fullName":"StudentService Input Validation and Edge Cases should validate date ranges","status":"passed","title":"should validate date ranges","duration":3.2737999999999374,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Integration Testing"],"fullName":"StudentService Integration Testing should maintain data consistency across operations","status":"passed","title":"should maintain data consistency across operations","duration":7.918300000000272,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Integration Testing"],"fullName":"StudentService Integration Testing should handle complex search scenarios","status":"passed","title":"should handle complex search scenarios","duration":3.528400000000147,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Integration Testing"],"fullName":"StudentService Integration Testing should maintain referential integrity","status":"passed","title":"should maintain referential integrity","duration":2.6217999999998938,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Error Recovery and Resilience"],"fullName":"StudentService Error Recovery and Resilience should recover from temporary database failures","status":"passed","title":"should recover from temporary database failures","duration":3.9470000000001164,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Error Recovery and Resilience"],"fullName":"StudentService Error Recovery and Resilience should handle partial operation failures gracefully","status":"passed","title":"should handle partial operation failures gracefully","duration":4.0996000000000095,"failureMessages":[],"meta":{}}],"startTime":1751345986986,"endTime":1751345987159.0996,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/services/StudentService.test.ts"},{"assertionResults":[{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlag Hook"],"fullName":"Feature Flag React Hooks useFeatureFlag Hook should return correct feature flag value","status":"failed","title":"should return correct feature flag value","duration":35.313100000000304,"failureMessages":["AssertionError: expected true to be false // Object.is equality\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\hooks\\shared\\useFeatureFlag.test.tsx:103:30\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlag Hook"],"fullName":"Feature Flag React Hooks useFeatureFlag Hook should return false for disabled features","status":"passed","title":"should return false for disabled features","duration":6.5052999999998065,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlag Hook"],"fullName":"Feature Flag React Hooks useFeatureFlag Hook should handle SSR safely","status":"failed","title":"should handle SSR safely","duration":12.601700000000164,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","ReferenceError: window is not defined\n    at getActiveElementDeep (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:8442:13)\n    at getSelectionInformation (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:8476:21)\n    at prepareForCommit (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10912:26)\n    at commitBeforeMutationEffects (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:22980:27)\n    at commitRootImpl (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26840:45)\n    at commitRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26721:5)\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26156:3)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26240:7)\n    at ReactDOMRoot.ReactDOMHydrationRoot.unmount.ReactDOMRoot.unmount [as unmount] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29375:5)"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlags Hook"],"fullName":"Feature Flag React Hooks useFeatureFlags Hook should return multiple feature flags","status":"failed","title":"should return multiple feature flags","duration":3.6811000000002423,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlags Hook"],"fullName":"Feature Flag React Hooks useFeatureFlags Hook should handle empty feature array","status":"failed","title":"should handle empty feature array","duration":2.9215000000003783,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useAllFeatureFlags Hook"],"fullName":"Feature Flag React Hooks useAllFeatureFlags Hook should return all feature flags","status":"failed","title":"should return all feature flags","duration":3.6311000000000604,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useEnabledFeatures Hook"],"fullName":"Feature Flag React Hooks useEnabledFeatures Hook should return only enabled features","status":"failed","title":"should return only enabled features","duration":3.3497000000002117,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlagAnalytics Hook"],"fullName":"Feature Flag React Hooks useFeatureFlagAnalytics Hook should return analytics data","status":"failed","title":"should return analytics data","duration":3.822599999999966,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlagValidation Hook"],"fullName":"Feature Flag React Hooks useFeatureFlagValidation Hook should return validation results","status":"failed","title":"should return validation results","duration":3.4131999999999607,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlagValidation Hook"],"fullName":"Feature Flag React Hooks useFeatureFlagValidation Hook should handle validation errors","status":"failed","title":"should handle validation errors","duration":4.452600000000075,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","withFeatureFlag HOC"],"fullName":"Feature Flag React Hooks withFeatureFlag HOC should render component when feature is enabled","status":"failed","title":"should render component when feature is enabled","duration":3.8627000000001317,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","withFeatureFlag HOC"],"fullName":"Feature Flag React Hooks withFeatureFlag HOC should not render component when feature is disabled","status":"failed","title":"should not render component when feature is disabled","duration":3.8809000000001106,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","withFeatureFlag HOC"],"fullName":"Feature Flag React Hooks withFeatureFlag HOC should render fallback component when feature is disabled","status":"failed","title":"should render fallback component when feature is disabled","duration":3.528200000000197,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","FeatureGate Component"],"fullName":"Feature Flag React Hooks FeatureGate Component should render children when feature is enabled","status":"failed","title":"should render children when feature is enabled","duration":5.850100000000111,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","FeatureGate Component"],"fullName":"Feature Flag React Hooks FeatureGate Component should not render children when feature is disabled","status":"failed","title":"should not render children when feature is disabled","duration":3.5763999999999214,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","FeatureGate Component"],"fullName":"Feature Flag React Hooks FeatureGate Component should render fallback when feature is disabled","status":"failed","title":"should render fallback when feature is disabled","duration":2.6732999999999265,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","FeatureDisabled Component"],"fullName":"Feature Flag React Hooks FeatureDisabled Component should render children when feature is disabled","status":"failed","title":"should render children when feature is disabled","duration":2.2986000000000786,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","FeatureDisabled Component"],"fullName":"Feature Flag React Hooks FeatureDisabled Component should not render children when feature is enabled","status":"failed","title":"should not render children when feature is enabled","duration":2.302900000000136,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlagDebug Hook"],"fullName":"Feature Flag React Hooks useFeatureFlagDebug Hook should provide debug functions","status":"failed","title":"should provide debug functions","duration":3.304099999999835,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","Utility Functions"],"fullName":"Feature Flag React Hooks Utility Functions isValidFeatureFlag should validate feature names","status":"passed","title":"isValidFeatureFlag should validate feature names","duration":1.32970000000023,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","Utility Functions"],"fullName":"Feature Flag React Hooks Utility Functions createFeatureDependentValue should return correct values","status":"passed","title":"createFeatureDependentValue should return correct values","duration":0.6635000000001128,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","Performance and Memory"],"fullName":"Feature Flag React Hooks Performance and Memory should not cause memory leaks with multiple renders","status":"failed","title":"should not cause memory leaks with multiple renders","duration":1.4234000000001288,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","Performance and Memory"],"fullName":"Feature Flag React Hooks Performance and Memory should memoize results correctly","status":"failed","title":"should memoize results correctly","duration":1.5302999999998974,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}}],"startTime":1751345986972,"endTime":1751345987091.5303,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/hooks/shared/useFeatureFlag.test.tsx"},{"assertionResults":[{"ancestorTitles":["Feature Flags System","Environment Variable Parsing"],"fullName":"Feature Flags System Environment Variable Parsing should parse true values correctly","status":"passed","title":"should parse true values correctly","duration":8.371999999999844,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Environment Variable Parsing"],"fullName":"Feature Flags System Environment Variable Parsing should parse false values correctly","status":"passed","title":"should parse false values correctly","duration":6.544199999999819,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Environment Variable Parsing"],"fullName":"Feature Flags System Environment Variable Parsing should handle case insensitive values","status":"failed","title":"should handle case insensitive values","duration":8.679599999999937,"failureMessages":["Error: Cannot find module '@/lib/config/FeatureFlags'\nRequire stack:\n- C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.resolve (node:internal/modules/helpers:193:19)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts:87:36\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Feature Flags System","Environment Variable Parsing"],"fullName":"Feature Flags System Environment Variable Parsing should use default values for undefined environment variables","status":"passed","title":"should use default values for undefined environment variables","duration":3.4794999999999163,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Environment Variable Parsing"],"fullName":"Feature Flags System Environment Variable Parsing should handle invalid environment variable values","status":"failed","title":"should handle invalid environment variable values","duration":3.850500000000011,"failureMessages":["Error: Cannot find module '@/lib/config/FeatureFlags'\nRequire stack:\n- C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.resolve (node:internal/modules/helpers:193:19)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts:114:36\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Functions"],"fullName":"Feature Flags System Feature Flag Functions isFeatureEnabled should return correct boolean values","status":"passed","title":"isFeatureEnabled should return correct boolean values","duration":1.0625,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Functions"],"fullName":"Feature Flags System Feature Flag Functions getAllFeatureFlags should return complete FeatureFlags object","status":"passed","title":"getAllFeatureFlags should return complete FeatureFlags object","duration":3.6678000000001703,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Functions"],"fullName":"Feature Flags System Feature Flag Functions getEnabledFeatures should return only enabled features","status":"failed","title":"getEnabledFeatures should return only enabled features","duration":3.4537999999997737,"failureMessages":["Error: Cannot find module '@/lib/config/FeatureFlags'\nRequire stack:\n- C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.resolve (node:internal/modules/helpers:193:19)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts:160:36\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Analytics"],"fullName":"Feature Flags System Feature Flag Analytics getFeatureFlagAnalytics should return correct analytics data","status":"passed","title":"getFeatureFlagAnalytics should return correct analytics data","duration":2.24980000000005,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Analytics"],"fullName":"Feature Flags System Feature Flag Analytics analytics should update when flags change","status":"failed","title":"analytics should update when flags change","duration":3.344100000000253,"failureMessages":["Error: Cannot find module '@/lib/config/FeatureFlags'\nRequire stack:\n- C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.resolve (node:internal/modules/helpers:193:19)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts:218:36\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Validation"],"fullName":"Feature Flags System Feature Flag Validation should validate production environment constraints","status":"failed","title":"should validate production environment constraints","duration":3.052100000000337,"failureMessages":["Error: Cannot find module '@/lib/config/FeatureFlags'\nRequire stack:\n- C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.resolve (node:internal/modules/helpers:193:19)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts:235:36\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Validation"],"fullName":"Feature Flags System Feature Flag Validation should validate feature dependencies","status":"failed","title":"should validate feature dependencies","duration":3.7880999999997584,"failureMessages":["Error: Cannot find module '@/lib/config/FeatureFlags'\nRequire stack:\n- C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.resolve (node:internal/modules/helpers:193:19)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts:252:36\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Validation"],"fullName":"Feature Flags System Feature Flag Validation should pass validation with correct configuration","status":"failed","title":"should pass validation with correct configuration","duration":3.384900000000016,"failureMessages":["Error: Cannot find module '@/lib/config/FeatureFlags'\nRequire stack:\n- C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.resolve (node:internal/modules/helpers:193:19)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts:270:36\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Feature Flags System","Development Mode Features"],"fullName":"Feature Flags System Development Mode Features should enable debug mode automatically in development","status":"passed","title":"should enable debug mode automatically in development","duration":8.710899999999583,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Development Mode Features"],"fullName":"Feature Flags System Development Mode Features should respect explicit debug mode setting","status":"failed","title":"should respect explicit debug mode setting","duration":2.646400000000085,"failureMessages":["Error: Cannot find module '@/lib/config/FeatureFlags'\nRequire stack:\n- C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.resolve (node:internal/modules/helpers:193:19)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts:296:36\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Feature Flags System","Edge Cases"],"fullName":"Feature Flags System Edge Cases should handle empty string environment variables","status":"failed","title":"should handle empty string environment variables","duration":2.4770000000003165,"failureMessages":["Error: Cannot find module '@/lib/config/FeatureFlags'\nRequire stack:\n- C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.resolve (node:internal/modules/helpers:193:19)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts:309:36\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Feature Flags System","Edge Cases"],"fullName":"Feature Flags System Edge Cases should handle whitespace in environment variables","status":"failed","title":"should handle whitespace in environment variables","duration":2.8526999999999134,"failureMessages":["Error: Cannot find module '@/lib/config/FeatureFlags'\nRequire stack:\n- C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.resolve (node:internal/modules/helpers:193:19)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts:322:36\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Feature Flags System","Edge Cases"],"fullName":"Feature Flags System Edge Cases should handle all feature flag keys","status":"passed","title":"should handle all feature flag keys","duration":5.214099999999689,"failureMessages":[],"meta":{}}],"startTime":1751345986952,"endTime":1751345987030.214,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/lib/config/FeatureFlags.test.ts"}]}
