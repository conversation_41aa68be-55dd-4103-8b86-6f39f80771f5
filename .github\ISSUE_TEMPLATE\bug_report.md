---
name: 🐛 Bug Report
about: Create a report to help us improve the RK Institute Management System
title: '[BUG] '
labels: ['bug', 'needs-triage']
assignees: ['IamNeoNerd']
---

# 🐛 Bug Report

## 📋 **Bug Information**

### **Bug Summary**

<!-- Provide a clear and concise description of the bug -->

### **Expected Behavior**

<!-- Describe what you expected to happen -->

### **Actual Behavior**

<!-- Describe what actually happened -->

### **Impact Level**

<!-- Mark the severity of this bug -->

- [ ] 🔴 Critical (System down, data loss, security issue)
- [ ] 🟠 High (Major feature broken, affects many users)
- [ ] 🟡 Medium (Feature partially broken, workaround available)
- [ ] 🟢 Low (Minor issue, cosmetic problem)

---

## 🔍 **Reproduction Steps**

### **Steps to Reproduce**

<!-- Provide detailed steps to reproduce the bug -->

1. Go to '...'
2. Click on '...'
3. Scroll down to '...'
4. See error

### **Frequency**

<!-- How often does this bug occur? -->

- [ ] Always (100%)
- [ ] Often (75-99%)
- [ ] Sometimes (25-74%)
- [ ] Rarely (1-24%)
- [ ] Once (Unable to reproduce)

---

## 🌐 **Environment Information**

### **System Details**

- **Browser**: [e.g., Chrome 91, Firefox 89, Safari 14]
- **Operating System**: [e.g., Windows 10, macOS 11, Ubuntu 20.04]
- **Device**: [e.g., Desktop, Mobile, Tablet]
- **Screen Resolution**: [e.g., 1920x1080, 375x667]

### **Application Details**

- **URL**: [e.g., https://rk-institute-management-system.vercel.app/admin]
- **User Role**: [e.g., Admin, Teacher, Parent, Student]
- **Feature Area**: [e.g., Fee Calculation, Academic Logs, Authentication]

---

## 📊 **Additional Context**

### **Error Messages**

<!-- Include any error messages, console logs, or stack traces -->

```
Paste error messages here
```

### **Screenshots/Videos**

<!-- Add screenshots or videos to help explain the problem -->

### **Related Issues**

<!-- Link any related issues -->

- Related to #
- Duplicate of #

---

## 🔒 **Security Considerations**

### **Security Impact**

<!-- Mark if this bug has security implications -->

- [ ] ✅ No security impact
- [ ] 🔒 Potential security vulnerability
- [ ] 🚨 Confirmed security issue

### **Data Sensitivity**

<!-- Mark if sensitive data is involved -->

- [ ] ✅ No sensitive data involved
- [ ] 📊 Contains user data
- [ ] 💰 Contains financial data
- [ ] 🔐 Contains authentication data

---

## 💡 **Suggested Solution**

<!-- If you have ideas on how to fix this bug, please share them -->

---

## ✅ **Acceptance Criteria**

<!-- Define what needs to be done to consider this bug fixed -->

- [ ] Bug is reproducible
- [ ] Root cause identified
- [ ] Fix implemented
- [ ] Fix tested
- [ ] No regression introduced
- [ ] Documentation updated (if needed)
