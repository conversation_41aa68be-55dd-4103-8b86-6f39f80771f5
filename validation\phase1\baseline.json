
> rk-institute-management-system@2.0.0 test
> vitest --run --reporter=json

The CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.
{"numTotalTestSuites":63,"numPassedTestSuites":15,"numFailedTestSuites":48,"numPendingTestSuites":0,"numTotalTests":155,"numPassedTests":83,"numFailedTests":72,"numPendingTests":0,"numTodoTests":0,"snapshot":{"added":0,"failure":false,"filesAdded":0,"filesRemoved":0,"filesRemovedList":[],"filesUnmatched":0,"filesUpdated":0,"matched":0,"total":0,"unchecked":0,"uncheckedKeysByFile":[],"unmatched":0,"updated":0,"didUpdate":false},"startTime":1751345493912,"success":false,"testResults":[{"assertionResults":[{"ancestorTitles":["Phase 1 Implementation Integration Tests","Feature Flags System"],"fullName":"Phase 1 Implementation Integration Tests Feature Flags System should provide consistent feature flag access","status":"passed","title":"should provide consistent feature flag access","duration":2.1689000000001215,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Feature Flags System"],"fullName":"Phase 1 Implementation Integration Tests Feature Flags System should return complete feature flags object","status":"passed","title":"should return complete feature flags object","duration":1.3642000000004373,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Feature Flags System"],"fullName":"Phase 1 Implementation Integration Tests Feature Flags System should provide analytics data","status":"passed","title":"should provide analytics data","duration":0.6754000000000815,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Feature Flags System"],"fullName":"Phase 1 Implementation Integration Tests Feature Flags System should validate feature flag configuration","status":"passed","title":"should validate feature flag configuration","duration":0.35629999999991924,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Service Layer Implementation"],"fullName":"Phase 1 Implementation Integration Tests Service Layer Implementation should create StudentService instance","status":"passed","title":"should create StudentService instance","duration":0.425499999999829,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Service Layer Implementation"],"fullName":"Phase 1 Implementation Integration Tests Service Layer Implementation should provide health status","status":"passed","title":"should provide health status","duration":2.332100000000082,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Service Layer Implementation"],"fullName":"Phase 1 Implementation Integration Tests Service Layer Implementation should handle service operations with proper error handling","status":"passed","title":"should handle service operations with proper error handling","duration":1.001299999999901,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Service Layer Implementation"],"fullName":"Phase 1 Implementation Integration Tests Service Layer Implementation should provide consistent service result format","status":"passed","title":"should provide consistent service result format","duration":0.8191999999999098,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Service Layer Implementation"],"fullName":"Phase 1 Implementation Integration Tests Service Layer Implementation should handle pagination correctly","status":"failed","title":"should handle pagination correctly","duration":10.867699999999786,"failureMessages":["AssertionError: expected false to be true // Object.is equality\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:196:49\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/vitest/dist/chunks/runBaseTests.9YDrdSI4.js:130:11"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should initialize module registry","status":"passed","title":"should initialize module registry","duration":0.5156999999999243,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should register modules successfully","status":"passed","title":"should register modules successfully","duration":2.2507000000000517,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should provide module status information","status":"failed","title":"should provide module status information","duration":2.4810999999999694,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:216:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should handle module dependencies correctly","status":"failed","title":"should handle module dependencies correctly","duration":1.5169000000000779,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:236:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should integrate with feature flags","status":"failed","title":"should integrate with feature flags","duration":1.2782999999999447,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:253:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should provide module statistics","status":"failed","title":"should provide module statistics","duration":1.1695999999997184,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:266:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","System Integration"],"fullName":"Phase 1 Implementation Integration Tests System Integration should integrate all Phase 1 components","status":"failed","title":"should integrate all Phase 1 components","duration":1.335099999999784,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:289:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","System Integration"],"fullName":"Phase 1 Implementation Integration Tests System Integration should maintain backwards compatibility","status":"failed","title":"should maintain backwards compatibility","duration":4.902999999999793,"failureMessages":["AssertionError: expected [Function] to not throw an error but 'Error: Module core is already registeΓÇª' was thrown\n    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/expect/dist/index.js:1437:21)\n    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/expect/dist/index.js:923:17)\n    at Proxy.methodWrapper (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/chai/chai.js:1618:25)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:317:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","System Integration"],"fullName":"Phase 1 Implementation Integration Tests System Integration should provide consistent error handling across systems","status":"passed","title":"should provide consistent error handling across systems","duration":2.14429999999993,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","System Integration"],"fullName":"Phase 1 Implementation Integration Tests System Integration should provide performance metrics","status":"failed","title":"should provide performance metrics","duration":1.4308999999998377,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:339:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Phase 1 Success Criteria Validation"],"fullName":"Phase 1 Implementation Integration Tests Phase 1 Success Criteria Validation should meet all Phase 1 success criteria","status":"failed","title":"should meet all Phase 1 success criteria","duration":1.0487000000002809,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:373:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Phase 1 Success Criteria Validation"],"fullName":"Phase 1 Implementation Integration Tests Phase 1 Success Criteria Validation should provide development experience improvements","status":"failed","title":"should provide development experience improvements","duration":0.9171999999998661,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:405:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Phase 1 Success Criteria Validation"],"fullName":"Phase 1 Implementation Integration Tests Phase 1 Success Criteria Validation should maintain performance standards","status":"failed","title":"should maintain performance standards","duration":0.925300000000334,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:420:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}}],"startTime":1751345496858,"endTime":1751345496900.9253,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/integration/Phase1Implementation.test.ts"},{"assertionResults":[{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should register all modules without errors","status":"passed","title":"should register all modules without errors","duration":6.417500000000018,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should register core modules first","status":"passed","title":"should register core modules first","duration":2.3366000000000895,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should register feature modules with proper dependencies","status":"passed","title":"should register feature modules with proper dependencies","duration":1.9756000000002132,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should handle feature flag controlled modules","status":"passed","title":"should handle feature flag controlled modules","duration":1.4225000000001273,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should register integration modules conditionally","status":"passed","title":"should register integration modules conditionally","duration":0.9319000000000415,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should register experimental modules conditionally","status":"passed","title":"should register experimental modules conditionally","duration":1.0363999999999578,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should provide comprehensive module status","status":"passed","title":"should provide comprehensive module status","duration":1.4538000000002285,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should have correct module counts","status":"passed","title":"should have correct module counts","duration":0.907799999999952,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should categorize modules correctly","status":"passed","title":"should categorize modules correctly","duration":1.0632999999997992,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should track module status correctly","status":"passed","title":"should track module status correctly","duration":0.9839000000001761,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should identify enabled core modules","status":"passed","title":"should identify enabled core modules","duration":0.7730000000001382,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should identify disabled feature-controlled modules","status":"passed","title":"should identify disabled feature-controlled modules","duration":0.5876000000002932,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Dependencies and Relationships"],"fullName":"Module Registration System Module Dependencies and Relationships should maintain proper dependency hierarchy","status":"passed","title":"should maintain proper dependency hierarchy","duration":4.216199999999844,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Dependencies and Relationships"],"fullName":"Module Registration System Module Dependencies and Relationships should track module dependents correctly","status":"passed","title":"should track module dependents correctly","duration":1.3097999999999956,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Dependencies and Relationships"],"fullName":"Module Registration System Module Dependencies and Relationships should prevent disabling modules with active dependents","status":"passed","title":"should prevent disabling modules with active dependents","duration":1.042399999999816,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Feature Flag Integration"],"fullName":"Module Registration System Feature Flag Integration should respect feature flag states during registration","status":"failed","title":"should respect feature flag states during registration","duration":16.296100000000024,"failureMessages":["AssertionError: expected true to be false // Object.is equality\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\modules\\ModuleRegistration.test.ts:254:53\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/vitest/dist/chunks/runBaseTests.9YDrdSI4.js:130:11"],"meta":{}},{"ancestorTitles":["Module Registration System","Feature Flag Integration"],"fullName":"Module Registration System Feature Flag Integration should handle modules with mixed feature requirements","status":"passed","title":"should handle modules with mixed feature requirements","duration":0.8573999999998705,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Performance and Scalability"],"fullName":"Module Registration System Performance and Scalability should register modules efficiently","status":"passed","title":"should register modules efficiently","duration":0.5628999999998996,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Performance and Scalability"],"fullName":"Module Registration System Performance and Scalability should provide status information efficiently","status":"passed","title":"should provide status information efficiently","duration":0.4916000000002896,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Performance and Scalability"],"fullName":"Module Registration System Performance and Scalability should handle module queries efficiently","status":"passed","title":"should handle module queries efficiently","duration":1.3361999999997352,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Error Handling and Resilience"],"fullName":"Module Registration System Error Handling and Resilience should handle registration errors gracefully","status":"passed","title":"should handle registration errors gracefully","duration":0.607600000000275,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Error Handling and Resilience"],"fullName":"Module Registration System Error Handling and Resilience should provide error information in status","status":"passed","title":"should provide error information in status","duration":0.5333999999998014,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Error Handling and Resilience"],"fullName":"Module Registration System Error Handling and Resilience should handle missing dependencies gracefully","status":"passed","title":"should handle missing dependencies gracefully","duration":0.6660000000001673,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Health and Monitoring"],"fullName":"Module Registration System Module Health and Monitoring should provide health status for modules","status":"passed","title":"should provide health status for modules","duration":2.5214999999998327,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Health and Monitoring"],"fullName":"Module Registration System Module Health and Monitoring should track module performance metrics","status":"passed","title":"should track module performance metrics","duration":1.0552999999999884,"failureMessages":[],"meta":{}}],"startTime":1751345496798,"endTime":1751345496851.0554,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/modules/ModuleRegistration.test.ts"},{"assertionResults":[{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should register a valid module successfully","status":"passed","title":"should register a valid module successfully","duration":3.917699999999968,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should validate module configuration","status":"passed","title":"should validate module configuration","duration":0.8743999999996959,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should prevent duplicate module registration","status":"passed","title":"should prevent duplicate module registration","duration":0.6238000000003012,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should validate dependencies exist","status":"passed","title":"should validate dependencies exist","duration":0.7703000000001339,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should detect circular dependencies","status":"failed","title":"should detect circular dependencies","duration":8.515900000000329,"failureMessages":["Error: Dependency module-b not found for module module-a\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:202:17)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\modules\\ModuleRegistry.test.ts:148:16\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should handle feature flag requirements","status":"passed","title":"should handle feature flag requirements","duration":6.625100000000202,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should set module metadata correctly","status":"passed","title":"should set module metadata correctly","duration":1.290599999999813,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should check if module is enabled","status":"passed","title":"should check if module is enabled","duration":0.7128999999999905,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should get all modules","status":"passed","title":"should get all modules","duration":1.8293000000003303,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should get enabled modules only","status":"passed","title":"should get enabled modules only","duration":0.974699999999757,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should get modules by category","status":"passed","title":"should get modules by category","duration":0.9889000000002852,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should get module dependencies","status":"passed","title":"should get module dependencies","duration":1.8903999999997723,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should get module dependents","status":"passed","title":"should get module dependents","duration":1.3069000000000415,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should track access metrics","status":"passed","title":"should track access metrics","duration":0.9861000000000786,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should check if module can be disabled","status":"passed","title":"should check if module can be disabled","duration":0.8402000000000953,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should disable module without dependents","status":"passed","title":"should disable module without dependents","duration":1.0318999999999505,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should prevent disabling module with dependents","status":"passed","title":"should prevent disabling module with dependents","duration":3.8398999999999432,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should enable disabled module","status":"passed","title":"should enable disabled module","duration":1.293999999999869,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should validate dependencies when enabling","status":"passed","title":"should validate dependencies when enabling","duration":1.3330000000000837,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should validate feature flags when enabling","status":"passed","title":"should validate feature flags when enabling","duration":0.694999999999709,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Event System"],"fullName":"ModuleRegistry Event System should emit registration events","status":"passed","title":"should emit registration events","duration":2.7695000000003347,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Event System"],"fullName":"ModuleRegistry Event System should emit enable/disable events","status":"passed","title":"should emit enable/disable events","duration":1.9524000000001251,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Event System"],"fullName":"ModuleRegistry Event System should handle event listener errors gracefully","status":"passed","title":"should handle event listener errors gracefully","duration":0.9535000000000764,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Event System"],"fullName":"ModuleRegistry Event System should remove event listeners","status":"passed","title":"should remove event listeners","duration":0.7591000000002168,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Health Monitoring"],"fullName":"ModuleRegistry Health Monitoring should perform health checks on all modules","status":"passed","title":"should perform health checks on all modules","duration":1.6763999999998305,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Health Monitoring"],"fullName":"ModuleRegistry Health Monitoring should emit health check events","status":"passed","title":"should emit health check events","duration":0.9855000000002292,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Health Monitoring"],"fullName":"ModuleRegistry Health Monitoring should detect dependency health issues","status":"passed","title":"should detect dependency health issues","duration":0.9506999999998698,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Statistics and Performance"],"fullName":"ModuleRegistry Statistics and Performance should provide comprehensive statistics","status":"passed","title":"should provide comprehensive statistics","duration":1.3461999999999534,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Statistics and Performance"],"fullName":"ModuleRegistry Statistics and Performance should track memory usage estimates","status":"passed","title":"should track memory usage estimates","duration":0.696400000000267,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Statistics and Performance"],"fullName":"ModuleRegistry Statistics and Performance should track load time performance","status":"passed","title":"should track load time performance","duration":0.6198000000003958,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Error Handling and Edge Cases"],"fullName":"ModuleRegistry Error Handling and Edge Cases should handle invalid module configurations gracefully","status":"passed","title":"should handle invalid module configurations gracefully","duration":2.0198999999997795,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Error Handling and Edge Cases"],"fullName":"ModuleRegistry Error Handling and Edge Cases should handle non-existent module operations","status":"passed","title":"should handle non-existent module operations","duration":1.0487000000002809,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Error Handling and Edge Cases"],"fullName":"ModuleRegistry Error Handling and Edge Cases should handle complex dependency chains","status":"passed","title":"should handle complex dependency chains","duration":1.1581999999998516,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Error Handling and Edge Cases"],"fullName":"ModuleRegistry Error Handling and Edge Cases should handle module registration errors","status":"passed","title":"should handle module registration errors","duration":1.1042999999999665,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Integration with Feature Flags"],"fullName":"ModuleRegistry Integration with Feature Flags should respect feature flag changes","status":"failed","title":"should respect feature flag changes","duration":5.485400000000027,"failureMessages":["TypeError: isFeatureEnabled.mockReturnValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\modules\\ModuleRegistry.test.ts:524:24\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/vitest/dist/chunks/runBaseTests.9YDrdSI4.js:130:11"],"meta":{}},{"ancestorTitles":["ModuleRegistry","Integration with Feature Flags"],"fullName":"ModuleRegistry Integration with Feature Flags should handle optional features correctly","status":"passed","title":"should handle optional features correctly","duration":5.071100000000115,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Performance Benchmarks"],"fullName":"ModuleRegistry Performance Benchmarks should register modules efficiently","status":"passed","title":"should register modules efficiently","duration":2.253400000000056,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Performance Benchmarks"],"fullName":"ModuleRegistry Performance Benchmarks should query modules efficiently","status":"passed","title":"should query modules efficiently","duration":2.8940999999999804,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Performance Benchmarks"],"fullName":"ModuleRegistry Performance Benchmarks should handle health checks efficiently","status":"passed","title":"should handle health checks efficiently","duration":0.8964000000000851,"failureMessages":[],"meta":{}}],"startTime":1751345496771,"endTime":1751345496847.8965,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/modules/ModuleRegistry.test.ts"},{"assertionResults":[{"ancestorTitles":["BaseService","Initialization and Connection Management"],"fullName":"BaseService Initialization and Connection Management should initialize Prisma client successfully","status":"failed","title":"should initialize Prisma client successfully","duration":54.66229999999996,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Initialization and Connection Management"],"fullName":"BaseService Initialization and Connection Management should handle database connection failure","status":"failed","title":"should handle database connection failure","duration":23.302999999999884,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Initialization and Connection Management"],"fullName":"BaseService Initialization and Connection Management should reuse existing Prisma client instance","status":"failed","title":"should reuse existing Prisma client instance","duration":10.116099999999733,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Error Handling"],"fullName":"BaseService Error Handling should handle unique constraint violation (P2002)","status":"failed","title":"should handle unique constraint violation (P2002)","duration":7.146200000000135,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Error Handling"],"fullName":"BaseService Error Handling should handle record not found (P2025)","status":"failed","title":"should handle record not found (P2025)","duration":6.86830000000009,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Error Handling"],"fullName":"BaseService Error Handling should handle foreign key constraint violation (P2003)","status":"failed","title":"should handle foreign key constraint violation (P2003)","duration":7.633500000000367,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Error Handling"],"fullName":"BaseService Error Handling should handle generic errors","status":"failed","title":"should handle generic errors","duration":16.246200000000044,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Error Handling"],"fullName":"BaseService Error Handling should include context in error metadata","status":"failed","title":"should include context in error metadata","duration":5.092999999999847,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Success Response Handling"],"fullName":"BaseService Success Response Handling should create successful response with data","status":"failed","title":"should create successful response with data","duration":8.131099999999606,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Success Response Handling"],"fullName":"BaseService Success Response Handling should include metadata in successful responses","status":"failed","title":"should include metadata in successful responses","duration":3.5667999999996027,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Pagination Utilities"],"fullName":"BaseService Pagination Utilities should validate and apply default pagination options","status":"failed","title":"should validate and apply default pagination options","duration":3.880700000000161,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Pagination Utilities"],"fullName":"BaseService Pagination Utilities should validate pagination options with custom values","status":"failed","title":"should validate pagination options with custom values","duration":4.34940000000006,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Pagination Utilities"],"fullName":"BaseService Pagination Utilities should enforce maximum limit of 100","status":"failed","title":"should enforce maximum limit of 100","duration":6.206500000000233,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Pagination Utilities"],"fullName":"BaseService Pagination Utilities should enforce minimum page of 1","status":"failed","title":"should enforce minimum page of 1","duration":4.783099999999649,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Pagination Utilities"],"fullName":"BaseService Pagination Utilities should build correct pagination metadata","status":"failed","title":"should build correct pagination metadata","duration":5.785200000000259,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Input Sanitization"],"fullName":"BaseService Input Sanitization should sanitize string inputs","status":"failed","title":"should sanitize string inputs","duration":15.983900000000176,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Input Sanitization"],"fullName":"BaseService Input Sanitization should sanitize object inputs recursively","status":"failed","title":"should sanitize object inputs recursively","duration":6.434499999999844,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Input Sanitization"],"fullName":"BaseService Input Sanitization should filter out dangerous keys","status":"failed","title":"should filter out dangerous keys","duration":6.751600000000053,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Field Validation"],"fullName":"BaseService Field Validation should validate required fields","status":"failed","title":"should validate required fields","duration":5.132900000000063,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Health Checks"],"fullName":"BaseService Health Checks should return healthy status when database is available","status":"failed","title":"should return healthy status when database is available","duration":5.115600000000086,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Health Checks"],"fullName":"BaseService Health Checks should return unhealthy status when database is unavailable","status":"failed","title":"should return unhealthy status when database is unavailable","duration":5.072400000000016,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Performance Monitoring"],"fullName":"BaseService Performance Monitoring should track operation duration","status":"failed","title":"should track operation duration","duration":5.895500000000084,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Performance Monitoring"],"fullName":"BaseService Performance Monitoring should include timestamp in metadata","status":"failed","title":"should include timestamp in metadata","duration":4.325600000000122,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Transaction Handling"],"fullName":"BaseService Transaction Handling should execute operations within transactions","status":"failed","title":"should execute operations within transactions","duration":5.675999999999931,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Transaction Handling"],"fullName":"BaseService Transaction Handling should handle transaction failures","status":"failed","title":"should handle transaction failures","duration":4.325900000000274,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Transaction Handling"],"fullName":"BaseService Transaction Handling should support custom transaction options","status":"failed","title":"should support custom transaction options","duration":7.24310000000014,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Cleanup and Disconnection"],"fullName":"BaseService Cleanup and Disconnection should disconnect from database","status":"failed","title":"should disconnect from database","duration":3.654700000000048,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["BaseService","Cleanup and Disconnection"],"fullName":"BaseService Cleanup and Disconnection should handle disconnect errors gracefully","status":"failed","title":"should handle disconnect errors gracefully","duration":3.2901999999999134,"failureMessages":["TypeError: getPrismaClient.mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:117:21\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}}],"startTime":1751345496841,"endTime":1751345497089.2903,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/services/BaseService.test.ts"},{"assertionResults":[],"startTime":1751345493912,"endTime":1751345493912,"status":"failed","message":"Transform failed with 1 error:\nC:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/services/StudentService.test.ts:47:33: ERROR: \"await\" can only be used inside an \"async\" function","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/services/StudentService.test.ts"},{"assertionResults":[],"startTime":1751345493912,"endTime":1751345493912,"status":"failed","message":"Transform failed with 1 error:\nC:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/simple/Phase1Validation.test.ts:227:29: ERROR: \"await\" can only be used inside an \"async\" function","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/simple/Phase1Validation.test.ts"},{"assertionResults":[{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlag Hook"],"fullName":"Feature Flag React Hooks useFeatureFlag Hook should return correct feature flag value","status":"failed","title":"should return correct feature flag value","duration":30.273499999999785,"failureMessages":["AssertionError: expected true to be false // Object.is equality\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\hooks\\shared\\useFeatureFlag.test.tsx:103:30\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlag Hook"],"fullName":"Feature Flag React Hooks useFeatureFlag Hook should return false for disabled features","status":"passed","title":"should return false for disabled features","duration":5.336099999999988,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlag Hook"],"fullName":"Feature Flag React Hooks useFeatureFlag Hook should handle SSR safely","status":"failed","title":"should handle SSR safely","duration":13.462099999999737,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","ReferenceError: window is not defined\n    at getActiveElementDeep (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:8442:13)\n    at getSelectionInformation (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:8476:21)\n    at prepareForCommit (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10912:26)\n    at commitBeforeMutationEffects (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:22980:27)\n    at commitRootImpl (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26840:45)\n    at commitRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26721:5)\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26156:3)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26240:7)\n    at ReactDOMRoot.ReactDOMHydrationRoot.unmount.ReactDOMRoot.unmount [as unmount] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29375:5)"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlags Hook"],"fullName":"Feature Flag React Hooks useFeatureFlags Hook should return multiple feature flags","status":"failed","title":"should return multiple feature flags","duration":4.033199999999852,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlags Hook"],"fullName":"Feature Flag React Hooks useFeatureFlags Hook should handle empty feature array","status":"failed","title":"should handle empty feature array","duration":3.18080000000009,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useAllFeatureFlags Hook"],"fullName":"Feature Flag React Hooks useAllFeatureFlags Hook should return all feature flags","status":"failed","title":"should return all feature flags","duration":2.984899999999925,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useEnabledFeatures Hook"],"fullName":"Feature Flag React Hooks useEnabledFeatures Hook should return only enabled features","status":"failed","title":"should return only enabled features","duration":3.706300000000283,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlagAnalytics Hook"],"fullName":"Feature Flag React Hooks useFeatureFlagAnalytics Hook should return analytics data","status":"failed","title":"should return analytics data","duration":2.8665000000000873,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlagValidation Hook"],"fullName":"Feature Flag React Hooks useFeatureFlagValidation Hook should return validation results","status":"failed","title":"should return validation results","duration":3.198599999999715,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlagValidation Hook"],"fullName":"Feature Flag React Hooks useFeatureFlagValidation Hook should handle validation errors","status":"failed","title":"should handle validation errors","duration":2.6107999999999265,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","withFeatureFlag HOC"],"fullName":"Feature Flag React Hooks withFeatureFlag HOC should render component when feature is enabled","status":"failed","title":"should render component when feature is enabled","duration":3.9332000000003973,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","withFeatureFlag HOC"],"fullName":"Feature Flag React Hooks withFeatureFlag HOC should not render component when feature is disabled","status":"failed","title":"should not render component when feature is disabled","duration":8.835900000000038,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","withFeatureFlag HOC"],"fullName":"Feature Flag React Hooks withFeatureFlag HOC should render fallback component when feature is disabled","status":"failed","title":"should render fallback component when feature is disabled","duration":14.903700000000299,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","FeatureGate Component"],"fullName":"Feature Flag React Hooks FeatureGate Component should render children when feature is enabled","status":"failed","title":"should render children when feature is enabled","duration":3.1296000000002095,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","FeatureGate Component"],"fullName":"Feature Flag React Hooks FeatureGate Component should not render children when feature is disabled","status":"failed","title":"should not render children when feature is disabled","duration":2.723200000000361,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","FeatureGate Component"],"fullName":"Feature Flag React Hooks FeatureGate Component should render fallback when feature is disabled","status":"failed","title":"should render fallback when feature is disabled","duration":2.917200000000321,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","FeatureDisabled Component"],"fullName":"Feature Flag React Hooks FeatureDisabled Component should render children when feature is disabled","status":"failed","title":"should render children when feature is disabled","duration":5.290100000000166,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","FeatureDisabled Component"],"fullName":"Feature Flag React Hooks FeatureDisabled Component should not render children when feature is enabled","status":"failed","title":"should not render children when feature is enabled","duration":2.9270000000001346,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlagDebug Hook"],"fullName":"Feature Flag React Hooks useFeatureFlagDebug Hook should provide debug functions","status":"failed","title":"should provide debug functions","duration":2.637300000000323,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","Utility Functions"],"fullName":"Feature Flag React Hooks Utility Functions isValidFeatureFlag should validate feature names","status":"passed","title":"isValidFeatureFlag should validate feature names","duration":1.888800000000174,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","Utility Functions"],"fullName":"Feature Flag React Hooks Utility Functions createFeatureDependentValue should return correct values","status":"passed","title":"createFeatureDependentValue should return correct values","duration":1.0683999999996558,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","Performance and Memory"],"fullName":"Feature Flag React Hooks Performance and Memory should not cause memory leaks with multiple renders","status":"failed","title":"should not cause memory leaks with multiple renders","duration":2.3951999999999316,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","Performance and Memory"],"fullName":"Feature Flag React Hooks Performance and Memory should memoize results correctly","status":"failed","title":"should memoize results correctly","duration":2.8708999999998923,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}}],"startTime":1751345496829,"endTime":1751345496959.8708,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/hooks/shared/useFeatureFlag.test.tsx"},{"assertionResults":[{"ancestorTitles":["Feature Flags System","Environment Variable Parsing"],"fullName":"Feature Flags System Environment Variable Parsing should parse true values correctly","status":"passed","title":"should parse true values correctly","duration":6.247600000000148,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Environment Variable Parsing"],"fullName":"Feature Flags System Environment Variable Parsing should parse false values correctly","status":"passed","title":"should parse false values correctly","duration":4.088799999999992,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Environment Variable Parsing"],"fullName":"Feature Flags System Environment Variable Parsing should handle case insensitive values","status":"failed","title":"should handle case insensitive values","duration":9.96349999999984,"failureMessages":["Error: Cannot find module '@/lib/config/FeatureFlags'\nRequire stack:\n- C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.resolve (node:internal/modules/helpers:193:19)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts:87:36\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Feature Flags System","Environment Variable Parsing"],"fullName":"Feature Flags System Environment Variable Parsing should use default values for undefined environment variables","status":"passed","title":"should use default values for undefined environment variables","duration":4.524300000000039,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Environment Variable Parsing"],"fullName":"Feature Flags System Environment Variable Parsing should handle invalid environment variable values","status":"failed","title":"should handle invalid environment variable values","duration":1.8268000000002758,"failureMessages":["Error: Cannot find module '@/lib/config/FeatureFlags'\nRequire stack:\n- C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.resolve (node:internal/modules/helpers:193:19)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts:114:36\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Functions"],"fullName":"Feature Flags System Feature Flag Functions isFeatureEnabled should return correct boolean values","status":"passed","title":"isFeatureEnabled should return correct boolean values","duration":0.535099999999602,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Functions"],"fullName":"Feature Flags System Feature Flag Functions getAllFeatureFlags should return complete FeatureFlags object","status":"passed","title":"getAllFeatureFlags should return complete FeatureFlags object","duration":1.67450000000008,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Functions"],"fullName":"Feature Flags System Feature Flag Functions getEnabledFeatures should return only enabled features","status":"failed","title":"getEnabledFeatures should return only enabled features","duration":2.529099999999744,"failureMessages":["Error: Cannot find module '@/lib/config/FeatureFlags'\nRequire stack:\n- C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.resolve (node:internal/modules/helpers:193:19)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts:160:36\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Analytics"],"fullName":"Feature Flags System Feature Flag Analytics getFeatureFlagAnalytics should return correct analytics data","status":"passed","title":"getFeatureFlagAnalytics should return correct analytics data","duration":1.165599999999813,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Analytics"],"fullName":"Feature Flags System Feature Flag Analytics analytics should update when flags change","status":"failed","title":"analytics should update when flags change","duration":2.516200000000026,"failureMessages":["Error: Cannot find module '@/lib/config/FeatureFlags'\nRequire stack:\n- C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.resolve (node:internal/modules/helpers:193:19)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts:218:36\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Validation"],"fullName":"Feature Flags System Feature Flag Validation should validate production environment constraints","status":"failed","title":"should validate production environment constraints","duration":1.9275000000002365,"failureMessages":["Error: Cannot find module '@/lib/config/FeatureFlags'\nRequire stack:\n- C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.resolve (node:internal/modules/helpers:193:19)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts:235:36\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Validation"],"fullName":"Feature Flags System Feature Flag Validation should validate feature dependencies","status":"failed","title":"should validate feature dependencies","duration":1.9103000000000065,"failureMessages":["Error: Cannot find module '@/lib/config/FeatureFlags'\nRequire stack:\n- C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.resolve (node:internal/modules/helpers:193:19)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts:252:36\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Validation"],"fullName":"Feature Flags System Feature Flag Validation should pass validation with correct configuration","status":"failed","title":"should pass validation with correct configuration","duration":1.8713999999999942,"failureMessages":["Error: Cannot find module '@/lib/config/FeatureFlags'\nRequire stack:\n- C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.resolve (node:internal/modules/helpers:193:19)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts:270:36\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Feature Flags System","Development Mode Features"],"fullName":"Feature Flags System Development Mode Features should enable debug mode automatically in development","status":"passed","title":"should enable debug mode automatically in development","duration":3.9621000000001914,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Development Mode Features"],"fullName":"Feature Flags System Development Mode Features should respect explicit debug mode setting","status":"failed","title":"should respect explicit debug mode setting","duration":2.8919999999998254,"failureMessages":["Error: Cannot find module '@/lib/config/FeatureFlags'\nRequire stack:\n- C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.resolve (node:internal/modules/helpers:193:19)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts:296:36\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Feature Flags System","Edge Cases"],"fullName":"Feature Flags System Edge Cases should handle empty string environment variables","status":"failed","title":"should handle empty string environment variables","duration":1.9152000000003682,"failureMessages":["Error: Cannot find module '@/lib/config/FeatureFlags'\nRequire stack:\n- C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.resolve (node:internal/modules/helpers:193:19)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts:309:36\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Feature Flags System","Edge Cases"],"fullName":"Feature Flags System Edge Cases should handle whitespace in environment variables","status":"failed","title":"should handle whitespace in environment variables","duration":2.130000000000109,"failureMessages":["Error: Cannot find module '@/lib/config/FeatureFlags'\nRequire stack:\n- C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.resolve (node:internal/modules/helpers:193:19)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\lib\\config\\FeatureFlags.test.ts:322:36\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Feature Flags System","Edge Cases"],"fullName":"Feature Flags System Edge Cases should handle all feature flag keys","status":"passed","title":"should handle all feature flag keys","duration":2.9139000000000124,"failureMessages":[],"meta":{}}],"startTime":1751345496788,"endTime":1751345496843.9138,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/lib/config/FeatureFlags.test.ts"}]}
