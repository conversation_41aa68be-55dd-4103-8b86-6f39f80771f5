The CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.
{"numTotalTestSuites":86,"numPassedTestSuites":56,"numFailedTestSuites":30,"numPendingTestSuites":0,"numTotalTests":225,"numPassedTests":189,"numFailedTests":36,"numPendingTests":0,"numTodoTests":0,"snapshot":{"added":0,"failure":false,"filesAdded":0,"filesRemoved":0,"filesRemovedList":[],"filesUnmatched":0,"filesUpdated":0,"matched":0,"total":0,"unchecked":0,"uncheckedKeysByFile":[],"unmatched":0,"updated":0,"didUpdate":false},"startTime":1751389386264,"success":false,"testResults":[{"assertionResults":[{"ancestorTitles":["Contract Testing: Mock Authenticity Validation","Business Logic Validation"],"fullName":"Contract Testing: Mock Authenticity Validation Business Logic Validation should validate that enhanced mocks reflect real business rules","status":"failed","title":"should validate that enhanced mocks reflect real business rules","duration":73.27969999999914,"failureMessages":["TypeError: __vite_ssr_import_0__.vi.mocked(...).mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\contract\\MockValidation.test.ts:24:32\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["Contract Testing: Mock Authenticity Validation","Business Logic Validation"],"fullName":"Contract Testing: Mock Authenticity Validation Business Logic Validation should validate duplicate student ID prevention","status":"failed","title":"should validate duplicate student ID prevention","duration":16.440999999998894,"failureMessages":["TypeError: __vite_ssr_import_0__.vi.mocked(...).mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\contract\\MockValidation.test.ts:24:32\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["Contract Testing: Mock Authenticity Validation","Business Logic Validation"],"fullName":"Contract Testing: Mock Authenticity Validation Business Logic Validation should validate pagination behavior authenticity","status":"failed","title":"should validate pagination behavior authenticity","duration":12.283400000000256,"failureMessages":["TypeError: __vite_ssr_import_0__.vi.mocked(...).mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\contract\\MockValidation.test.ts:24:32\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["Contract Testing: Mock Authenticity Validation","Mock vs Reality Gap Analysis"],"fullName":"Contract Testing: Mock Authenticity Validation Mock vs Reality Gap Analysis should identify gaps between mock and real implementation","status":"failed","title":"should identify gaps between mock and real implementation","duration":11.916900000000169,"failureMessages":["TypeError: __vite_ssr_import_0__.vi.mocked(...).mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\contract\\MockValidation.test.ts:24:32\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["Contract Testing: Mock Authenticity Validation","Mock vs Reality Gap Analysis"],"fullName":"Contract Testing: Mock Authenticity Validation Mock vs Reality Gap Analysis should validate that enhanced mocks catch previously hidden issues","status":"failed","title":"should validate that enhanced mocks catch previously hidden issues","duration":13.020000000000437,"failureMessages":["TypeError: __vite_ssr_import_0__.vi.mocked(...).mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\contract\\MockValidation.test.ts:24:32\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}},{"ancestorTitles":["Contract Testing: Mock Authenticity Validation","Test Authenticity Assessment"],"fullName":"Contract Testing: Mock Authenticity Validation Test Authenticity Assessment should assess confidence level in test results","status":"failed","title":"should assess confidence level in test results","duration":18.117400000002817,"failureMessages":["TypeError: __vite_ssr_import_0__.vi.mocked(...).mockResolvedValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\contract\\MockValidation.test.ts:24:32\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:890:22)\n    at callSuiteHook (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:879:10)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:966:30)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)"],"meta":{}}],"startTime":1751389418763,"endTime":1751389418909.1174,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/contract/MockValidation.test.ts"},{"assertionResults":[{"ancestorTitles":["Phase 1 Implementation Integration Tests","Feature Flags System"],"fullName":"Phase 1 Implementation Integration Tests Feature Flags System should provide consistent feature flag access","status":"passed","title":"should provide consistent feature flag access","duration":15.90559999999823,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Feature Flags System"],"fullName":"Phase 1 Implementation Integration Tests Feature Flags System should return complete feature flags object","status":"passed","title":"should return complete feature flags object","duration":10.984199999998964,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Feature Flags System"],"fullName":"Phase 1 Implementation Integration Tests Feature Flags System should provide analytics data","status":"passed","title":"should provide analytics data","duration":8.984200000002602,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Feature Flags System"],"fullName":"Phase 1 Implementation Integration Tests Feature Flags System should validate feature flag configuration","status":"passed","title":"should validate feature flag configuration","duration":7.119099999999889,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Service Layer Implementation"],"fullName":"Phase 1 Implementation Integration Tests Service Layer Implementation should create StudentService instance","status":"passed","title":"should create StudentService instance","duration":14.382999999997992,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Service Layer Implementation"],"fullName":"Phase 1 Implementation Integration Tests Service Layer Implementation should provide health status","status":"passed","title":"should provide health status","duration":13.017299999999523,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Service Layer Implementation"],"fullName":"Phase 1 Implementation Integration Tests Service Layer Implementation should handle service operations with proper error handling","status":"passed","title":"should handle service operations with proper error handling","duration":13.124599999999191,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Service Layer Implementation"],"fullName":"Phase 1 Implementation Integration Tests Service Layer Implementation should provide consistent service result format","status":"passed","title":"should provide consistent service result format","duration":9.744399999999587,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Service Layer Implementation"],"fullName":"Phase 1 Implementation Integration Tests Service Layer Implementation should handle pagination correctly","status":"passed","title":"should handle pagination correctly","duration":11.181600000003527,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should initialize module registry","status":"passed","title":"should initialize module registry","duration":9.936900000000605,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should register modules successfully","status":"passed","title":"should register modules successfully","duration":10.301699999999983,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should provide module status information","status":"passed","title":"should provide module status information","duration":14.027099999999336,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should handle module dependencies correctly","status":"passed","title":"should handle module dependencies correctly","duration":24.162199999998847,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should integrate with feature flags","status":"passed","title":"should integrate with feature flags","duration":10.031899999998132,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should provide module statistics","status":"passed","title":"should provide module statistics","duration":15.6137000000017,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","System Integration"],"fullName":"Phase 1 Implementation Integration Tests System Integration should integrate all Phase 1 components","status":"passed","title":"should integrate all Phase 1 components","duration":8.665699999997742,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","System Integration"],"fullName":"Phase 1 Implementation Integration Tests System Integration should maintain backwards compatibility","status":"passed","title":"should maintain backwards compatibility","duration":6.964199999998527,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","System Integration"],"fullName":"Phase 1 Implementation Integration Tests System Integration should provide consistent error handling across systems","status":"passed","title":"should provide consistent error handling across systems","duration":8.496399999999994,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","System Integration"],"fullName":"Phase 1 Implementation Integration Tests System Integration should provide performance metrics","status":"passed","title":"should provide performance metrics","duration":8.940699999999197,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Phase 1 Success Criteria Validation"],"fullName":"Phase 1 Implementation Integration Tests Phase 1 Success Criteria Validation should meet all Phase 1 success criteria","status":"passed","title":"should meet all Phase 1 success criteria","duration":9.906100000000151,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Phase 1 Success Criteria Validation"],"fullName":"Phase 1 Implementation Integration Tests Phase 1 Success Criteria Validation should provide development experience improvements","status":"passed","title":"should provide development experience improvements","duration":24.608000000000175,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Phase 1 Success Criteria Validation"],"fullName":"Phase 1 Implementation Integration Tests Phase 1 Success Criteria Validation should maintain performance standards","status":"passed","title":"should maintain performance standards","duration":7.229999999999563,"failureMessages":[],"meta":{}}],"startTime":1751389418776,"endTime":1751389419041.23,"status":"passed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/integration/Phase1Implementation.test.ts"},{"assertionResults":[{"ancestorTitles":["BaseService","Initialization and Connection Management"],"fullName":"BaseService Initialization and Connection Management should initialize Prisma client successfully","status":"failed","title":"should initialize Prisma client successfully","duration":28.608000000000175,"failureMessages":["AssertionError: expected false to be true // Object.is equality\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:135:30\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/vitest/dist/chunks/runBaseTests.9YDrdSI4.js:130:11"],"meta":{}},{"ancestorTitles":["BaseService","Initialization and Connection Management"],"fullName":"BaseService Initialization and Connection Management should handle database connection failure","status":"passed","title":"should handle database connection failure","duration":11.142800000001444,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Initialization and Connection Management"],"fullName":"BaseService Initialization and Connection Management should reuse existing Prisma client instance","status":"passed","title":"should reuse existing Prisma client instance","duration":10.741900000000896,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Error Handling"],"fullName":"BaseService Error Handling should handle unique constraint violation (P2002)","status":"passed","title":"should handle unique constraint violation (P2002)","duration":10.804799999998068,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Error Handling"],"fullName":"BaseService Error Handling should handle record not found (P2025)","status":"passed","title":"should handle record not found (P2025)","duration":9.09769999999844,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Error Handling"],"fullName":"BaseService Error Handling should handle foreign key constraint violation (P2003)","status":"passed","title":"should handle foreign key constraint violation (P2003)","duration":10.731299999999464,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Error Handling"],"fullName":"BaseService Error Handling should handle generic errors","status":"passed","title":"should handle generic errors","duration":9.208300000002055,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Error Handling"],"fullName":"BaseService Error Handling should include context in error metadata","status":"passed","title":"should include context in error metadata","duration":11.268999999996595,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Success Response Handling"],"fullName":"BaseService Success Response Handling should create successful response with data","status":"failed","title":"should create successful response with data","duration":10.759299999997893,"failureMessages":["AssertionError: expected false to be true // Object.is equality\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:219:30\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/vitest/dist/chunks/runBaseTests.9YDrdSI4.js:130:11"],"meta":{}},{"ancestorTitles":["BaseService","Success Response Handling"],"fullName":"BaseService Success Response Handling should include metadata in successful responses","status":"failed","title":"should include metadata in successful responses","duration":12.229899999998452,"failureMessages":["AssertionError: expected false to be true // Object.is equality\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:229:30\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/vitest/dist/chunks/runBaseTests.9YDrdSI4.js:130:11"],"meta":{}},{"ancestorTitles":["BaseService","Pagination Utilities"],"fullName":"BaseService Pagination Utilities should validate and apply default pagination options","status":"failed","title":"should validate and apply default pagination options","duration":18.95379999999932,"failureMessages":["AssertionError: expected false to be true // Object.is equality\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:241:30\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/vitest/dist/chunks/runBaseTests.9YDrdSI4.js:130:11"],"meta":{}},{"ancestorTitles":["BaseService","Pagination Utilities"],"fullName":"BaseService Pagination Utilities should validate pagination options with custom values","status":"passed","title":"should validate pagination options with custom values","duration":28.12880000000223,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Pagination Utilities"],"fullName":"BaseService Pagination Utilities should enforce maximum limit of 100","status":"passed","title":"should enforce maximum limit of 100","duration":10.359599999999773,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Pagination Utilities"],"fullName":"BaseService Pagination Utilities should enforce minimum page of 1","status":"passed","title":"should enforce minimum page of 1","duration":13.704900000000634,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Pagination Utilities"],"fullName":"BaseService Pagination Utilities should build correct pagination metadata","status":"passed","title":"should build correct pagination metadata","duration":12.362100000002101,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Input Sanitization"],"fullName":"BaseService Input Sanitization should sanitize string inputs","status":"passed","title":"should sanitize string inputs","duration":11.527899999997317,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Input Sanitization"],"fullName":"BaseService Input Sanitization should sanitize object inputs recursively","status":"passed","title":"should sanitize object inputs recursively","duration":18.805700000000797,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Input Sanitization"],"fullName":"BaseService Input Sanitization should filter out dangerous keys","status":"passed","title":"should filter out dangerous keys","duration":24.409700000000157,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Field Validation"],"fullName":"BaseService Field Validation should validate required fields","status":"passed","title":"should validate required fields","duration":9.65350000000035,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Health Checks"],"fullName":"BaseService Health Checks should return healthy status when database is available","status":"passed","title":"should return healthy status when database is available","duration":8.16530000000057,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Health Checks"],"fullName":"BaseService Health Checks should return unhealthy status when database is unavailable","status":"passed","title":"should return unhealthy status when database is unavailable","duration":10.609599999999773,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Performance Monitoring"],"fullName":"BaseService Performance Monitoring should track operation duration","status":"failed","title":"should track operation duration","duration":18.567599999998492,"failureMessages":["AssertionError: expected undefined not to be undefined\n    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/expect/dist/index.js:1247:24)\n    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/expect/dist/index.js:923:17)\n    at Proxy.methodWrapper (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/chai/chai.js:1618:25)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:391:41\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["BaseService","Performance Monitoring"],"fullName":"BaseService Performance Monitoring should include timestamp in metadata","status":"passed","title":"should include timestamp in metadata","duration":11.690800000000309,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Transaction Handling"],"fullName":"BaseService Transaction Handling should execute operations within transactions","status":"passed","title":"should execute operations within transactions","duration":13.11160000000018,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Transaction Handling"],"fullName":"BaseService Transaction Handling should handle transaction failures","status":"passed","title":"should handle transaction failures","duration":9.102399999999761,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Transaction Handling"],"fullName":"BaseService Transaction Handling should support custom transaction options","status":"passed","title":"should support custom transaction options","duration":12.398999999997613,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Cleanup and Disconnection"],"fullName":"BaseService Cleanup and Disconnection should disconnect from database","status":"passed","title":"should disconnect from database","duration":10.880999999997584,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Cleanup and Disconnection"],"fullName":"BaseService Cleanup and Disconnection should handle disconnect errors gracefully","status":"passed","title":"should handle disconnect errors gracefully","duration":9.968600000000151,"failureMessages":[],"meta":{}}],"startTime":1751389418770,"endTime":1751389419149.9685,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/services/BaseService.test.ts"},{"assertionResults":[{"ancestorTitles":["StudentService","Service Initialization"],"fullName":"StudentService Service Initialization should create StudentService instance","status":"passed","title":"should create StudentService instance","duration":18.64559999999983,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Service Initialization"],"fullName":"StudentService Service Initialization should inherit from BaseService","status":"passed","title":"should inherit from BaseService","duration":10.820700000000215,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Create Student Operations"],"fullName":"StudentService Create Student Operations should create student successfully with valid data","status":"failed","title":"should create student successfully with valid data","duration":29.8112000000001,"failureMessages":["AssertionError: expected false to be true // Object.is equality\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\StudentService.test.ts:83:30\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/vitest/dist/chunks/runBaseTests.9YDrdSI4.js:130:11"],"meta":{}},{"ancestorTitles":["StudentService","Create Student Operations"],"fullName":"StudentService Create Student Operations should validate required fields","status":"passed","title":"should validate required fields","duration":12.169499999999971,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Create Student Operations"],"fullName":"StudentService Create Student Operations should validate family exists","status":"passed","title":"should validate family exists","duration":13.525699999998324,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Create Student Operations"],"fullName":"StudentService Create Student Operations should check for duplicate student ID","status":"passed","title":"should check for duplicate student ID","duration":9.737499999999272,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Create Student Operations"],"fullName":"StudentService Create Student Operations should handle database errors during creation","status":"failed","title":"should handle database errors during creation","duration":19.07170000000042,"failureMessages":["AssertionError: expected 'DUPLICATE_STUDENT_ID' to be 'UNIQUE_CONSTRAINT_VIOLATION' // Object.is equality\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\StudentService.test.ts:144:27\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/vitest/dist/chunks/runBaseTests.9YDrdSI4.js:130:11"],"meta":{}},{"ancestorTitles":["StudentService","Create Student Operations"],"fullName":"StudentService Create Student Operations should set default enrollment date if not provided","status":"failed","title":"should set default enrollment date if not provided","duration":22.64849999999933,"failureMessages":["AssertionError: expected \"spy\" to be called with arguments: [ { data: ObjectContaining{ΓÇª}, ΓÇª(1) } ]\u001b[90m\n\nReceived: \n\n\u001b[39m\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/expect/dist/index.js:1393:13)\n    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/expect/dist/index.js:923:17)\n    at Proxy.methodWrapper (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/chai/chai.js:1618:25)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\StudentService.test.ts:154:41\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["StudentService","Find Student Operations"],"fullName":"StudentService Find Student Operations should find student by ID successfully","status":"passed","title":"should find student by ID successfully","duration":11.924999999999272,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Find Student Operations"],"fullName":"StudentService Find Student Operations should handle student not found","status":"passed","title":"should handle student not found","duration":24.083000000002357,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Find Student Operations"],"fullName":"StudentService Find Student Operations should validate student ID format","status":"passed","title":"should validate student ID format","duration":17.661800000001676,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Find Student Operations"],"fullName":"StudentService Find Student Operations should find students by family ID","status":"passed","title":"should find students by family ID","duration":10.094399999998132,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Update Student Operations"],"fullName":"StudentService Update Student Operations should update student successfully","status":"failed","title":"should update student successfully","duration":26.73660000000018,"failureMessages":["AssertionError: expected { id: 'student-123', ΓÇª(12) } to deeply equal { id: 'student-123', ΓÇª(12) }\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\StudentService.test.ts:229:27\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/vitest/dist/chunks/runBaseTests.9YDrdSI4.js:130:11"],"meta":{}},{"ancestorTitles":["StudentService","Update Student Operations"],"fullName":"StudentService Update Student Operations should validate student exists before update","status":"passed","title":"should validate student exists before update","duration":7.6251999999985856,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Update Student Operations"],"fullName":"StudentService Update Student Operations should check for duplicate student ID during update","status":"passed","title":"should check for duplicate student ID during update","duration":26.14190000000235,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Update Student Operations"],"fullName":"StudentService Update Student Operations should allow updating to same student ID","status":"passed","title":"should allow updating to same student ID","duration":7.9078000000008615,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Delete Student Operations"],"fullName":"StudentService Delete Student Operations should soft delete student successfully","status":"passed","title":"should soft delete student successfully","duration":9.282400000000052,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Delete Student Operations"],"fullName":"StudentService Delete Student Operations should validate student exists before deletion","status":"passed","title":"should validate student exists before deletion","duration":11.278600000001461,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Delete Student Operations"],"fullName":"StudentService Delete Student Operations should prevent deletion of already inactive student","status":"passed","title":"should prevent deletion of already inactive student","duration":20.096000000001368,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Search and Pagination"],"fullName":"StudentService Search and Pagination should find many students with default pagination","status":"passed","title":"should find many students with default pagination","duration":13.881799999999203,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Search and Pagination"],"fullName":"StudentService Search and Pagination should apply search filters correctly","status":"passed","title":"should apply search filters correctly","duration":11.530899999997928,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Search and Pagination"],"fullName":"StudentService Search and Pagination should handle date range filters","status":"passed","title":"should handle date range filters","duration":11.25959999999759,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Search and Pagination"],"fullName":"StudentService Search and Pagination should include inactive students when requested","status":"passed","title":"should include inactive students when requested","duration":14.054400000000896,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Statistics and Analytics"],"fullName":"StudentService Statistics and Analytics should get student statistics","status":"passed","title":"should get student statistics","duration":10.13749999999709,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Statistics and Analytics"],"fullName":"StudentService Statistics and Analytics should handle empty statistics gracefully","status":"passed","title":"should handle empty statistics gracefully","duration":9.923999999999069,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Performance Testing"],"fullName":"StudentService Performance Testing should handle large datasets efficiently","status":"passed","title":"should handle large datasets efficiently","duration":9.179299999999785,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Performance Testing"],"fullName":"StudentService Performance Testing should track operation performance metrics","status":"passed","title":"should track operation performance metrics","duration":10.429000000000087,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Performance Testing"],"fullName":"StudentService Performance Testing should handle concurrent operations","status":"passed","title":"should handle concurrent operations","duration":31.006799999999203,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Input Validation and Edge Cases"],"fullName":"StudentService Input Validation and Edge Cases should handle invalid input data gracefully","status":"passed","title":"should handle invalid input data gracefully","duration":9.377300000000105,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Input Validation and Edge Cases"],"fullName":"StudentService Input Validation and Edge Cases should handle edge case data","status":"failed","title":"should handle edge case data","duration":10.316100000000006,"failureMessages":["AssertionError: expected \"spy\" to be called at least once\n    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/expect/dist/index.js:1376:13)\n    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/expect/dist/index.js:923:17)\n    at Proxy.methodWrapper (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/chai/chai.js:1618:25)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\StudentService.test.ts:537:41\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["StudentService","Input Validation and Edge Cases"],"fullName":"StudentService Input Validation and Edge Cases should validate date ranges","status":"passed","title":"should validate date ranges","duration":9.6916999999994,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Integration Testing"],"fullName":"StudentService Integration Testing should maintain data consistency across operations","status":"failed","title":"should maintain data consistency across operations","duration":19.565099999999802,"failureMessages":["AssertionError: expected false to be true // Object.is equality\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\StudentService.test.ts:578:35\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/vitest/dist/chunks/runBaseTests.9YDrdSI4.js:130:11"],"meta":{}},{"ancestorTitles":["StudentService","Integration Testing"],"fullName":"StudentService Integration Testing should handle complex search scenarios","status":"passed","title":"should handle complex search scenarios","duration":7.917199999999866,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Integration Testing"],"fullName":"StudentService Integration Testing should maintain referential integrity","status":"passed","title":"should maintain referential integrity","duration":5.494699999999284,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Error Recovery and Resilience"],"fullName":"StudentService Error Recovery and Resilience should recover from temporary database failures","status":"passed","title":"should recover from temporary database failures","duration":5.9222999999983585,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Error Recovery and Resilience"],"fullName":"StudentService Error Recovery and Resilience should handle partial operation failures gracefully","status":"passed","title":"should handle partial operation failures gracefully","duration":6.299300000002404,"failureMessages":[],"meta":{}}],"startTime":1751389418770,"endTime":1751389419279.2993,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/services/StudentService.test.ts"},{"assertionResults":[{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should register all modules without errors","status":"passed","title":"should register all modules without errors","duration":11.569900000002235,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should register core modules first","status":"passed","title":"should register core modules first","duration":4.732799999997951,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should register feature modules with proper dependencies","status":"passed","title":"should register feature modules with proper dependencies","duration":4.661000000000058,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should handle feature flag controlled modules","status":"passed","title":"should handle feature flag controlled modules","duration":3.662000000000262,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should register integration modules conditionally","status":"passed","title":"should register integration modules conditionally","duration":3.4627999999975145,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should register experimental modules conditionally","status":"passed","title":"should register experimental modules conditionally","duration":4.146199999999226,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should provide comprehensive module status","status":"passed","title":"should provide comprehensive module status","duration":5.516999999999825,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should have correct module counts","status":"passed","title":"should have correct module counts","duration":5.103900000001886,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should categorize modules correctly","status":"passed","title":"should categorize modules correctly","duration":7.694400000000314,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should track module status correctly","status":"passed","title":"should track module status correctly","duration":6.799800000000687,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should identify enabled core modules","status":"passed","title":"should identify enabled core modules","duration":5.507999999997992,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should identify disabled feature-controlled modules","status":"passed","title":"should identify disabled feature-controlled modules","duration":4.304599999999482,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Dependencies and Relationships"],"fullName":"Module Registration System Module Dependencies and Relationships should maintain proper dependency hierarchy","status":"passed","title":"should maintain proper dependency hierarchy","duration":7.013800000000629,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Dependencies and Relationships"],"fullName":"Module Registration System Module Dependencies and Relationships should track module dependents correctly","status":"passed","title":"should track module dependents correctly","duration":5.528200000000652,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Dependencies and Relationships"],"fullName":"Module Registration System Module Dependencies and Relationships should prevent disabling modules with active dependents","status":"passed","title":"should prevent disabling modules with active dependents","duration":4.941300000002229,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Feature Flag Integration"],"fullName":"Module Registration System Feature Flag Integration should respect feature flag states during registration","status":"passed","title":"should respect feature flag states during registration","duration":7.935199999999895,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Feature Flag Integration"],"fullName":"Module Registration System Feature Flag Integration should handle modules with mixed feature requirements","status":"passed","title":"should handle modules with mixed feature requirements","duration":6.151599999997416,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Performance and Scalability"],"fullName":"Module Registration System Performance and Scalability should register modules efficiently","status":"passed","title":"should register modules efficiently","duration":6.4693999999981315,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Performance and Scalability"],"fullName":"Module Registration System Performance and Scalability should provide status information efficiently","status":"passed","title":"should provide status information efficiently","duration":5.401099999999133,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Performance and Scalability"],"fullName":"Module Registration System Performance and Scalability should handle module queries efficiently","status":"passed","title":"should handle module queries efficiently","duration":8.433700000001409,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Error Handling and Resilience"],"fullName":"Module Registration System Error Handling and Resilience should handle registration errors gracefully","status":"passed","title":"should handle registration errors gracefully","duration":5.886599999997998,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Error Handling and Resilience"],"fullName":"Module Registration System Error Handling and Resilience should provide error information in status","status":"passed","title":"should provide error information in status","duration":6.349299999998038,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Error Handling and Resilience"],"fullName":"Module Registration System Error Handling and Resilience should handle missing dependencies gracefully","status":"passed","title":"should handle missing dependencies gracefully","duration":7.769500000002154,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Health and Monitoring"],"fullName":"Module Registration System Module Health and Monitoring should provide health status for modules","status":"passed","title":"should provide health status for modules","duration":8.571599999999307,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Health and Monitoring"],"fullName":"Module Registration System Module Health and Monitoring should track module performance metrics","status":"passed","title":"should track module performance metrics","duration":5.606100000000879,"failureMessages":[],"meta":{}}],"startTime":1751389418722,"endTime":1751389418877.6062,"status":"passed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/modules/ModuleRegistration.test.ts"},{"assertionResults":[{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should register a valid module successfully","status":"passed","title":"should register a valid module successfully","duration":9.869200000001001,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should validate module configuration","status":"passed","title":"should validate module configuration","duration":5.550600000002305,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should prevent duplicate module registration","status":"passed","title":"should prevent duplicate module registration","duration":4.992600000001403,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should validate dependencies exist","status":"passed","title":"should validate dependencies exist","duration":3.5975999999973283,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should detect circular dependencies","status":"passed","title":"should detect circular dependencies","duration":5.039699999997538,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should handle feature flag requirements","status":"passed","title":"should handle feature flag requirements","duration":10.157300000002579,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should set module metadata correctly","status":"passed","title":"should set module metadata correctly","duration":4.824099999997998,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should check if module is enabled","status":"passed","title":"should check if module is enabled","duration":4.385600000001432,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should get all modules","status":"passed","title":"should get all modules","duration":7.533299999999144,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should get enabled modules only","status":"passed","title":"should get enabled modules only","duration":5.5222000000030675,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should get modules by category","status":"passed","title":"should get modules by category","duration":8.074699999997392,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should get module dependencies","status":"passed","title":"should get module dependencies","duration":5.707800000000134,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should get module dependents","status":"passed","title":"should get module dependents","duration":5.12329999999929,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should track access metrics","status":"passed","title":"should track access metrics","duration":6.13470000000234,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should check if module can be disabled","status":"passed","title":"should check if module can be disabled","duration":5.354299999999057,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should disable module without dependents","status":"passed","title":"should disable module without dependents","duration":4.688099999999395,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should prevent disabling module with dependents","status":"passed","title":"should prevent disabling module with dependents","duration":5.531399999999849,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should enable disabled module","status":"passed","title":"should enable disabled module","duration":5.7458000000005995,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should validate dependencies when enabling","status":"passed","title":"should validate dependencies when enabling","duration":6.892499999998108,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should validate feature flags when enabling","status":"passed","title":"should validate feature flags when enabling","duration":5.435199999999895,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Event System"],"fullName":"ModuleRegistry Event System should emit registration events","status":"passed","title":"should emit registration events","duration":7.443999999999505,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Event System"],"fullName":"ModuleRegistry Event System should emit enable/disable events","status":"passed","title":"should emit enable/disable events","duration":6.613199999999779,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Event System"],"fullName":"ModuleRegistry Event System should handle event listener errors gracefully","status":"passed","title":"should handle event listener errors gracefully","duration":6.515500000001339,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Event System"],"fullName":"ModuleRegistry Event System should remove event listeners","status":"passed","title":"should remove event listeners","duration":8.151499999999942,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Health Monitoring"],"fullName":"ModuleRegistry Health Monitoring should perform health checks on all modules","status":"passed","title":"should perform health checks on all modules","duration":5.677499999997963,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Health Monitoring"],"fullName":"ModuleRegistry Health Monitoring should emit health check events","status":"passed","title":"should emit health check events","duration":5.3686000000016065,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Health Monitoring"],"fullName":"ModuleRegistry Health Monitoring should detect dependency health issues","status":"passed","title":"should detect dependency health issues","duration":3.8125,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Statistics and Performance"],"fullName":"ModuleRegistry Statistics and Performance should provide comprehensive statistics","status":"passed","title":"should provide comprehensive statistics","duration":7.469300000000658,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Statistics and Performance"],"fullName":"ModuleRegistry Statistics and Performance should track memory usage estimates","status":"passed","title":"should track memory usage estimates","duration":5.849499999996624,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Statistics and Performance"],"fullName":"ModuleRegistry Statistics and Performance should track load time performance","status":"passed","title":"should track load time performance","duration":4.595800000002782,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Error Handling and Edge Cases"],"fullName":"ModuleRegistry Error Handling and Edge Cases should handle invalid module configurations gracefully","status":"passed","title":"should handle invalid module configurations gracefully","duration":11.721600000000763,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Error Handling and Edge Cases"],"fullName":"ModuleRegistry Error Handling and Edge Cases should handle non-existent module operations","status":"passed","title":"should handle non-existent module operations","duration":5.275399999998626,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Error Handling and Edge Cases"],"fullName":"ModuleRegistry Error Handling and Edge Cases should handle complex dependency chains","status":"passed","title":"should handle complex dependency chains","duration":6.245999999999185,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Error Handling and Edge Cases"],"fullName":"ModuleRegistry Error Handling and Edge Cases should handle module registration errors","status":"passed","title":"should handle module registration errors","duration":19.497400000000198,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Integration with Feature Flags"],"fullName":"ModuleRegistry Integration with Feature Flags should respect feature flag changes","status":"passed","title":"should respect feature flag changes","duration":7.414199999999255,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Integration with Feature Flags"],"fullName":"ModuleRegistry Integration with Feature Flags should handle optional features correctly","status":"passed","title":"should handle optional features correctly","duration":17.391700000000128,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Performance Benchmarks"],"fullName":"ModuleRegistry Performance Benchmarks should register modules efficiently","status":"passed","title":"should register modules efficiently","duration":7.477400000003399,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Performance Benchmarks"],"fullName":"ModuleRegistry Performance Benchmarks should query modules efficiently","status":"passed","title":"should query modules efficiently","duration":7.934499999999389,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Performance Benchmarks"],"fullName":"ModuleRegistry Performance Benchmarks should handle health checks efficiently","status":"passed","title":"should handle health checks efficiently","duration":6.202099999998609,"failureMessages":[],"meta":{}}],"startTime":1751389418712,"endTime":1751389418986.2021,"status":"passed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/modules/ModuleRegistry.test.ts"},{"assertionResults":[{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Feature Flags System"],"fullName":"Phase 1 Implementation - Simple Validation Feature Flags System should import feature flags module without errors","status":"passed","title":"should import feature flags module without errors","duration":46.03610000000117,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Feature Flags System"],"fullName":"Phase 1 Implementation - Simple Validation Feature Flags System should provide isFeatureEnabled function","status":"passed","title":"should provide isFeatureEnabled function","duration":7.04149999999936,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Feature Flags System"],"fullName":"Phase 1 Implementation - Simple Validation Feature Flags System should provide getAllFeatureFlags function","status":"passed","title":"should provide getAllFeatureFlags function","duration":6.919000000001688,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Feature Flags System"],"fullName":"Phase 1 Implementation - Simple Validation Feature Flags System should return boolean values for feature flags","status":"passed","title":"should return boolean values for feature flags","duration":10.474900000001071,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Feature Flags System"],"fullName":"Phase 1 Implementation - Simple Validation Feature Flags System should return complete feature flags object","status":"passed","title":"should return complete feature flags object","duration":13.373900000002322,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Feature Flags System"],"fullName":"Phase 1 Implementation - Simple Validation Feature Flags System should provide analytics functionality","status":"passed","title":"should provide analytics functionality","duration":12.862900000000081,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Service Layer Implementation"],"fullName":"Phase 1 Implementation - Simple Validation Service Layer Implementation should import BaseService without errors","status":"passed","title":"should import BaseService without errors","duration":8.128700000001118,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Service Layer Implementation"],"fullName":"Phase 1 Implementation - Simple Validation Service Layer Implementation should import StudentService without errors","status":"passed","title":"should import StudentService without errors","duration":9.896499999998923,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Service Layer Implementation"],"fullName":"Phase 1 Implementation - Simple Validation Service Layer Implementation should provide BaseService class","status":"passed","title":"should provide BaseService class","duration":16.47899999999936,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Service Layer Implementation"],"fullName":"Phase 1 Implementation - Simple Validation Service Layer Implementation should provide StudentService class","status":"passed","title":"should provide StudentService class","duration":16.98029999999926,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Service Layer Implementation"],"fullName":"Phase 1 Implementation - Simple Validation Service Layer Implementation should create StudentService instance","status":"passed","title":"should create StudentService instance","duration":19.204600000000937,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Service Layer Implementation"],"fullName":"Phase 1 Implementation - Simple Validation Service Layer Implementation should provide required service methods","status":"passed","title":"should provide required service methods","duration":16.08770000000004,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Service Layer Implementation"],"fullName":"Phase 1 Implementation - Simple Validation Service Layer Implementation should provide ServiceResult interface types","status":"passed","title":"should provide ServiceResult interface types","duration":15.223799999999756,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should import ModuleRegistry without errors","status":"passed","title":"should import ModuleRegistry without errors","duration":8.28900000000067,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should import module registration without errors","status":"passed","title":"should import module registration without errors","duration":12.19340000000011,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should provide ModuleRegistry class","status":"passed","title":"should provide ModuleRegistry class","duration":29.788899999999558,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should provide global moduleRegistry instance","status":"passed","title":"should provide global moduleRegistry instance","duration":20.07800000000134,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should provide module registration functions","status":"passed","title":"should provide module registration functions","duration":12.5512000000017,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should register modules successfully","status":"passed","title":"should register modules successfully","duration":15.525000000001455,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should provide module status information","status":"passed","title":"should provide module status information","duration":15.697700000000623,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should handle module dependencies","status":"passed","title":"should handle module dependencies","duration":29.75460000000021,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should provide module statistics","status":"passed","title":"should provide module statistics","duration":11.162000000000262,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","System Integration"],"fullName":"Phase 1 Implementation - Simple Validation System Integration should integrate all Phase 1 components without errors","status":"passed","title":"should integrate all Phase 1 components without errors","duration":8.366699999998673,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","System Integration"],"fullName":"Phase 1 Implementation - Simple Validation System Integration should maintain backwards compatibility","status":"passed","title":"should maintain backwards compatibility","duration":9.03020000000106,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","System Integration"],"fullName":"Phase 1 Implementation - Simple Validation System Integration should provide consistent interfaces","status":"passed","title":"should provide consistent interfaces","duration":32.82419999999911,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Phase 1 Success Criteria"],"fullName":"Phase 1 Implementation - Simple Validation Phase 1 Success Criteria should meet all Phase 1 success criteria","status":"passed","title":"should meet all Phase 1 success criteria","duration":23.80160000000251,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Phase 1 Success Criteria"],"fullName":"Phase 1 Implementation - Simple Validation Phase 1 Success Criteria should provide development experience improvements","status":"passed","title":"should provide development experience improvements","duration":24.362499999999272,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Phase 1 Success Criteria"],"fullName":"Phase 1 Implementation - Simple Validation Phase 1 Success Criteria should maintain performance standards","status":"passed","title":"should maintain performance standards","duration":16.37999999999738,"failureMessages":[],"meta":{}}],"startTime":1751389418692,"endTime":1751389419163.38,"status":"passed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/simple/Phase1Validation.test.ts"},{"assertionResults":[{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlag Hook"],"fullName":"Feature Flag React Hooks useFeatureFlag Hook should return correct feature flag value","status":"passed","title":"should return correct feature flag value","duration":26.534999999999854,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlag Hook"],"fullName":"Feature Flag React Hooks useFeatureFlag Hook should return false for disabled features","status":"passed","title":"should return false for disabled features","duration":9.244200000001001,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlag Hook"],"fullName":"Feature Flag React Hooks useFeatureFlag Hook should handle SSR safely","status":"failed","title":"should handle SSR safely","duration":16.533400000000256,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","ReferenceError: window is not defined\n    at getActiveElementDeep (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:8442:13)\n    at getSelectionInformation (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:8476:21)\n    at prepareForCommit (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10912:26)\n    at commitBeforeMutationEffects (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:22980:27)\n    at commitRootImpl (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26840:45)\n    at commitRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26721:5)\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26156:3)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26240:7)\n    at ReactDOMRoot.ReactDOMHydrationRoot.unmount.ReactDOMRoot.unmount [as unmount] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29375:5)"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlags Hook"],"fullName":"Feature Flag React Hooks useFeatureFlags Hook should return multiple feature flags","status":"failed","title":"should return multiple feature flags","duration":3.058000000000902,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlags Hook"],"fullName":"Feature Flag React Hooks useFeatureFlags Hook should handle empty feature array","status":"failed","title":"should handle empty feature array","duration":3.7845000000015716,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useAllFeatureFlags Hook"],"fullName":"Feature Flag React Hooks useAllFeatureFlags Hook should return all feature flags","status":"failed","title":"should return all feature flags","duration":5.062400000002526,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useEnabledFeatures Hook"],"fullName":"Feature Flag React Hooks useEnabledFeatures Hook should return only enabled features","status":"failed","title":"should return only enabled features","duration":2.70509999999922,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlagAnalytics Hook"],"fullName":"Feature Flag React Hooks useFeatureFlagAnalytics Hook should return analytics data","status":"failed","title":"should return analytics data","duration":4.190699999999197,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlagValidation Hook"],"fullName":"Feature Flag React Hooks useFeatureFlagValidation Hook should return validation results","status":"failed","title":"should return validation results","duration":2.162799999998242,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlagValidation Hook"],"fullName":"Feature Flag React Hooks useFeatureFlagValidation Hook should handle validation errors","status":"failed","title":"should handle validation errors","duration":2.751399999997375,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","withFeatureFlag HOC"],"fullName":"Feature Flag React Hooks withFeatureFlag HOC should render component when feature is enabled","status":"failed","title":"should render component when feature is enabled","duration":2.8549000000020897,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","withFeatureFlag HOC"],"fullName":"Feature Flag React Hooks withFeatureFlag HOC should not render component when feature is disabled","status":"failed","title":"should not render component when feature is disabled","duration":3.002700000000914,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","withFeatureFlag HOC"],"fullName":"Feature Flag React Hooks withFeatureFlag HOC should render fallback component when feature is disabled","status":"failed","title":"should render fallback component when feature is disabled","duration":4.886199999997189,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","FeatureGate Component"],"fullName":"Feature Flag React Hooks FeatureGate Component should render children when feature is enabled","status":"failed","title":"should render children when feature is enabled","duration":4.698100000001432,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","FeatureGate Component"],"fullName":"Feature Flag React Hooks FeatureGate Component should not render children when feature is disabled","status":"failed","title":"should not render children when feature is disabled","duration":2.9717000000018743,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","FeatureGate Component"],"fullName":"Feature Flag React Hooks FeatureGate Component should render fallback when feature is disabled","status":"failed","title":"should render fallback when feature is disabled","duration":5.6414999999979045,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","FeatureDisabled Component"],"fullName":"Feature Flag React Hooks FeatureDisabled Component should render children when feature is disabled","status":"failed","title":"should render children when feature is disabled","duration":3.574300000000221,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","FeatureDisabled Component"],"fullName":"Feature Flag React Hooks FeatureDisabled Component should not render children when feature is enabled","status":"failed","title":"should not render children when feature is enabled","duration":1.877199999998993,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlagDebug Hook"],"fullName":"Feature Flag React Hooks useFeatureFlagDebug Hook should provide debug functions","status":"failed","title":"should provide debug functions","duration":1.6509000000005472,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","Utility Functions"],"fullName":"Feature Flag React Hooks Utility Functions isValidFeatureFlag should validate feature names","status":"passed","title":"isValidFeatureFlag should validate feature names","duration":5.854000000002998,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","Utility Functions"],"fullName":"Feature Flag React Hooks Utility Functions createFeatureDependentValue should return correct values","status":"passed","title":"createFeatureDependentValue should return correct values","duration":6.104999999999563,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","Performance and Memory"],"fullName":"Feature Flag React Hooks Performance and Memory should not cause memory leaks with multiple renders","status":"failed","title":"should not cause memory leaks with multiple renders","duration":1.5272000000004482,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","Performance and Memory"],"fullName":"Feature Flag React Hooks Performance and Memory should memoize results correctly","status":"failed","title":"should memoize results correctly","duration":1.8542000000015832,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}}],"startTime":1751389418754,"endTime":1751389418878.8542,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/hooks/shared/useFeatureFlag.test.tsx"},{"assertionResults":[{"ancestorTitles":["Feature Flags System","Environment Variable Parsing"],"fullName":"Feature Flags System Environment Variable Parsing should parse true values correctly","status":"passed","title":"should parse true values correctly","duration":12.424999999999272,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Environment Variable Parsing"],"fullName":"Feature Flags System Environment Variable Parsing should parse false values correctly","status":"passed","title":"should parse false values correctly","duration":12.464899999999034,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Environment Variable Parsing"],"fullName":"Feature Flags System Environment Variable Parsing should handle case insensitive values","status":"passed","title":"should handle case insensitive values","duration":12.566800000000512,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Environment Variable Parsing"],"fullName":"Feature Flags System Environment Variable Parsing should use default values for undefined environment variables","status":"passed","title":"should use default values for undefined environment variables","duration":11.337299999999232,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Environment Variable Parsing"],"fullName":"Feature Flags System Environment Variable Parsing should handle invalid environment variable values","status":"passed","title":"should handle invalid environment variable values","duration":12.787599999999657,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Functions"],"fullName":"Feature Flags System Feature Flag Functions isFeatureEnabled should return correct boolean values","status":"passed","title":"isFeatureEnabled should return correct boolean values","duration":7.396199999999226,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Functions"],"fullName":"Feature Flags System Feature Flag Functions getAllFeatureFlags should return complete FeatureFlags object","status":"passed","title":"getAllFeatureFlags should return complete FeatureFlags object","duration":10.246100000000297,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Functions"],"fullName":"Feature Flags System Feature Flag Functions getEnabledFeatures should return only enabled features","status":"passed","title":"getEnabledFeatures should return only enabled features","duration":14.327100000002247,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Analytics"],"fullName":"Feature Flags System Feature Flag Analytics getFeatureFlagAnalytics should return correct analytics data","status":"passed","title":"getFeatureFlagAnalytics should return correct analytics data","duration":8.746999999999389,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Analytics"],"fullName":"Feature Flags System Feature Flag Analytics analytics should update when flags change","status":"passed","title":"analytics should update when flags change","duration":12.812900000000809,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Validation"],"fullName":"Feature Flags System Feature Flag Validation should validate production environment constraints","status":"passed","title":"should validate production environment constraints","duration":13.075099999998201,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Validation"],"fullName":"Feature Flags System Feature Flag Validation should validate feature dependencies","status":"passed","title":"should validate feature dependencies","duration":11.16990000000078,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Validation"],"fullName":"Feature Flags System Feature Flag Validation should pass validation with correct configuration","status":"passed","title":"should pass validation with correct configuration","duration":9.485599999999977,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Development Mode Features"],"fullName":"Feature Flags System Development Mode Features should enable debug mode automatically in development","status":"passed","title":"should enable debug mode automatically in development","duration":16.20130000000063,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Development Mode Features"],"fullName":"Feature Flags System Development Mode Features should respect explicit debug mode setting","status":"passed","title":"should respect explicit debug mode setting","duration":8.014299999998912,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Edge Cases"],"fullName":"Feature Flags System Edge Cases should handle empty string environment variables","status":"passed","title":"should handle empty string environment variables","duration":27.364299999997456,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Edge Cases"],"fullName":"Feature Flags System Edge Cases should handle whitespace in environment variables","status":"passed","title":"should handle whitespace in environment variables","duration":17.204100000002654,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Edge Cases"],"fullName":"Feature Flags System Edge Cases should handle all feature flag keys","status":"passed","title":"should handle all feature flag keys","duration":10.906199999997625,"failureMessages":[],"meta":{}}],"startTime":1751389418742,"endTime":1751389418972.9062,"status":"passed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/lib/config/FeatureFlags.test.ts"}]}
