{"version": 2, "name": "rk-institute-management-system", "builds": [{"src": "package.json", "use": "@vercel/next"}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/(.*)", "dest": "/$1"}], "env": {"NODE_ENV": "production"}, "functions": {"pages/api/**/*.js": {"maxDuration": 30}, "pages/api/auth/**/*.js": {"maxDuration": 10}, "pages/api/health/**/*.js": {"maxDuration": 5}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "https://your-production-domain.com"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "redirects": [{"source": "/admin", "destination": "/dashboard/admin", "permanent": true}, {"source": "/login", "destination": "/auth/login", "permanent": true}], "rewrites": [{"source": "/health", "destination": "/api/health"}], "crons": [{"path": "/api/cron/backup", "schedule": "0 2 * * *"}, {"path": "/api/cron/cleanup", "schedule": "0 3 * * 0"}]}