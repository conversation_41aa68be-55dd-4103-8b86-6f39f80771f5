
> rk-institute-management-system@2.0.0 test
> vitest --run --reporter=json

The CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.
{"numTotalTestSuites":81,"numPassedTestSuites":45,"numFailedTestSuites":36,"numPendingTestSuites":0,"numTotalTests":219,"numPassedTests":180,"numFailedTests":39,"numPendingTests":0,"numTodoTests":0,"snapshot":{"added":0,"failure":false,"filesAdded":0,"filesRemoved":0,"filesRemovedList":[],"filesUnmatched":0,"filesUpdated":0,"matched":0,"total":0,"unchecked":0,"uncheckedKeysByFile":[],"unmatched":0,"updated":0,"didUpdate":false},"startTime":1751348473768,"success":false,"testResults":[{"assertionResults":[{"ancestorTitles":["Phase 1 Implementation Integration Tests","Feature Flags System"],"fullName":"Phase 1 Implementation Integration Tests Feature Flags System should provide consistent feature flag access","status":"passed","title":"should provide consistent feature flag access","duration":9.349300000000312,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Feature Flags System"],"fullName":"Phase 1 Implementation Integration Tests Feature Flags System should return complete feature flags object","status":"passed","title":"should return complete feature flags object","duration":7.501699999999801,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Feature Flags System"],"fullName":"Phase 1 Implementation Integration Tests Feature Flags System should provide analytics data","status":"passed","title":"should provide analytics data","duration":7.263500000000022,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Feature Flags System"],"fullName":"Phase 1 Implementation Integration Tests Feature Flags System should validate feature flag configuration","status":"passed","title":"should validate feature flag configuration","duration":5.731999999999971,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Service Layer Implementation"],"fullName":"Phase 1 Implementation Integration Tests Service Layer Implementation should create StudentService instance","status":"passed","title":"should create StudentService instance","duration":5.221499999999651,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Service Layer Implementation"],"fullName":"Phase 1 Implementation Integration Tests Service Layer Implementation should provide health status","status":"passed","title":"should provide health status","duration":10.735400000000027,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Service Layer Implementation"],"fullName":"Phase 1 Implementation Integration Tests Service Layer Implementation should handle service operations with proper error handling","status":"passed","title":"should handle service operations with proper error handling","duration":6.40059999999994,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Service Layer Implementation"],"fullName":"Phase 1 Implementation Integration Tests Service Layer Implementation should provide consistent service result format","status":"passed","title":"should provide consistent service result format","duration":6.3409999999998945,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Service Layer Implementation"],"fullName":"Phase 1 Implementation Integration Tests Service Layer Implementation should handle pagination correctly","status":"failed","title":"should handle pagination correctly","duration":16.254300000000057,"failureMessages":["AssertionError: expected false to be true // Object.is equality\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:202:49\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/vitest/dist/chunks/runBaseTests.9YDrdSI4.js:130:11"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should initialize module registry","status":"passed","title":"should initialize module registry","duration":6.342499999999745,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should register modules successfully","status":"passed","title":"should register modules successfully","duration":8.701500000000124,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should provide module status information","status":"failed","title":"should provide module status information","duration":30.467499999999745,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:222:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should handle module dependencies correctly","status":"failed","title":"should handle module dependencies correctly","duration":7.292000000000371,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:242:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should integrate with feature flags","status":"failed","title":"should integrate with feature flags","duration":8.788900000000012,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:259:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Module Registry System"],"fullName":"Phase 1 Implementation Integration Tests Module Registry System should provide module statistics","status":"failed","title":"should provide module statistics","duration":5.794199999999819,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:272:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","System Integration"],"fullName":"Phase 1 Implementation Integration Tests System Integration should integrate all Phase 1 components","status":"failed","title":"should integrate all Phase 1 components","duration":17.329400000000078,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:295:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","System Integration"],"fullName":"Phase 1 Implementation Integration Tests System Integration should maintain backwards compatibility","status":"failed","title":"should maintain backwards compatibility","duration":8.35629999999992,"failureMessages":["AssertionError: expected [Function] to not throw an error but 'Error: Module core is already registeΓÇª' was thrown\n    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/expect/dist/index.js:1437:21)\n    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/expect/dist/index.js:923:17)\n    at Proxy.methodWrapper (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/chai/chai.js:1618:25)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:323:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","System Integration"],"fullName":"Phase 1 Implementation Integration Tests System Integration should provide consistent error handling across systems","status":"passed","title":"should provide consistent error handling across systems","duration":5.887400000000071,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","System Integration"],"fullName":"Phase 1 Implementation Integration Tests System Integration should provide performance metrics","status":"failed","title":"should provide performance metrics","duration":5.795900000000074,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:345:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Phase 1 Success Criteria Validation"],"fullName":"Phase 1 Implementation Integration Tests Phase 1 Success Criteria Validation should meet all Phase 1 success criteria","status":"failed","title":"should meet all Phase 1 success criteria","duration":20.71439999999984,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:379:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Phase 1 Success Criteria Validation"],"fullName":"Phase 1 Implementation Integration Tests Phase 1 Success Criteria Validation should provide development experience improvements","status":"failed","title":"should provide development experience improvements","duration":5.0621000000001,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:411:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation Integration Tests","Phase 1 Success Criteria Validation"],"fullName":"Phase 1 Implementation Integration Tests Phase 1 Success Criteria Validation should maintain performance standards","status":"failed","title":"should maintain performance standards","duration":5.887699999999768,"failureMessages":["Error: Module core is already registered\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:196:15)\n    at registerCoreModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:62:18)\n    at Module.registerModules (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\index.ts:27:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\integration\\Phase1Implementation.test.ts:426:7\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}}],"startTime":1751348476772,"endTime":1751348476985.8877,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/integration/Phase1Implementation.test.ts"},{"assertionResults":[{"ancestorTitles":["BaseService","Initialization and Connection Management"],"fullName":"BaseService Initialization and Connection Management should initialize Prisma client successfully","status":"failed","title":"should initialize Prisma client successfully","duration":17.426699999999983,"failureMessages":["AssertionError: expected \"spy\" to be called at least once\n    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/expect/dist/index.js:1376:13)\n    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/expect/dist/index.js:923:17)\n    at Proxy.methodWrapper (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/chai/chai.js:1618:25)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:132:35\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["BaseService","Initialization and Connection Management"],"fullName":"BaseService Initialization and Connection Management should handle database connection failure","status":"failed","title":"should handle database connection failure","duration":4.715199999999641,"failureMessages":["Error: Database connection failed for TestModel service\n    at TestService.init (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\services\\BaseService.ts:159:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestService.findById (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:40:5)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\BaseService.test.ts:139:22\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)"],"meta":{}},{"ancestorTitles":["BaseService","Initialization and Connection Management"],"fullName":"BaseService Initialization and Connection Management should reuse existing Prisma client instance","status":"passed","title":"should reuse existing Prisma client instance","duration":2.1237999999998465,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Error Handling"],"fullName":"BaseService Error Handling should handle unique constraint violation (P2002)","status":"passed","title":"should handle unique constraint violation (P2002)","duration":8.769800000000032,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Error Handling"],"fullName":"BaseService Error Handling should handle record not found (P2025)","status":"passed","title":"should handle record not found (P2025)","duration":2.758100000000013,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Error Handling"],"fullName":"BaseService Error Handling should handle foreign key constraint violation (P2003)","status":"passed","title":"should handle foreign key constraint violation (P2003)","duration":4.154300000000148,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Error Handling"],"fullName":"BaseService Error Handling should handle generic errors","status":"passed","title":"should handle generic errors","duration":4.348599999999806,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Error Handling"],"fullName":"BaseService Error Handling should include context in error metadata","status":"passed","title":"should include context in error metadata","duration":4.795300000000225,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Success Response Handling"],"fullName":"BaseService Success Response Handling should create successful response with data","status":"passed","title":"should create successful response with data","duration":4.87079999999969,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Success Response Handling"],"fullName":"BaseService Success Response Handling should include metadata in successful responses","status":"passed","title":"should include metadata in successful responses","duration":5.538099999999758,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Pagination Utilities"],"fullName":"BaseService Pagination Utilities should validate and apply default pagination options","status":"passed","title":"should validate and apply default pagination options","duration":6.223300000000108,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Pagination Utilities"],"fullName":"BaseService Pagination Utilities should validate pagination options with custom values","status":"passed","title":"should validate pagination options with custom values","duration":3.150000000000091,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Pagination Utilities"],"fullName":"BaseService Pagination Utilities should enforce maximum limit of 100","status":"passed","title":"should enforce maximum limit of 100","duration":2.6217999999998938,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Pagination Utilities"],"fullName":"BaseService Pagination Utilities should enforce minimum page of 1","status":"passed","title":"should enforce minimum page of 1","duration":2.9817000000002736,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Pagination Utilities"],"fullName":"BaseService Pagination Utilities should build correct pagination metadata","status":"passed","title":"should build correct pagination metadata","duration":2.728599999999915,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Input Sanitization"],"fullName":"BaseService Input Sanitization should sanitize string inputs","status":"passed","title":"should sanitize string inputs","duration":2.284400000000005,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Input Sanitization"],"fullName":"BaseService Input Sanitization should sanitize object inputs recursively","status":"passed","title":"should sanitize object inputs recursively","duration":1.8261999999999716,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Input Sanitization"],"fullName":"BaseService Input Sanitization should filter out dangerous keys","status":"passed","title":"should filter out dangerous keys","duration":2.1567999999997483,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Field Validation"],"fullName":"BaseService Field Validation should validate required fields","status":"passed","title":"should validate required fields","duration":2.953100000000177,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Health Checks"],"fullName":"BaseService Health Checks should return healthy status when database is available","status":"passed","title":"should return healthy status when database is available","duration":4.017499999999927,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Health Checks"],"fullName":"BaseService Health Checks should return unhealthy status when database is unavailable","status":"passed","title":"should return unhealthy status when database is unavailable","duration":4.273299999999836,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Performance Monitoring"],"fullName":"BaseService Performance Monitoring should track operation duration","status":"passed","title":"should track operation duration","duration":5.055400000000191,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Performance Monitoring"],"fullName":"BaseService Performance Monitoring should include timestamp in metadata","status":"passed","title":"should include timestamp in metadata","duration":11.17739999999958,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Transaction Handling"],"fullName":"BaseService Transaction Handling should execute operations within transactions","status":"passed","title":"should execute operations within transactions","duration":7.798600000000079,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Transaction Handling"],"fullName":"BaseService Transaction Handling should handle transaction failures","status":"passed","title":"should handle transaction failures","duration":6.73739999999998,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Transaction Handling"],"fullName":"BaseService Transaction Handling should support custom transaction options","status":"passed","title":"should support custom transaction options","duration":4.683500000000095,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Cleanup and Disconnection"],"fullName":"BaseService Cleanup and Disconnection should disconnect from database","status":"passed","title":"should disconnect from database","duration":4.592399999999998,"failureMessages":[],"meta":{}},{"ancestorTitles":["BaseService","Cleanup and Disconnection"],"fullName":"BaseService Cleanup and Disconnection should handle disconnect errors gracefully","status":"passed","title":"should handle disconnect errors gracefully","duration":5.325400000000172,"failureMessages":[],"meta":{}}],"startTime":1751348476791,"endTime":1751348476934.3254,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/services/BaseService.test.ts"},{"assertionResults":[{"ancestorTitles":["StudentService","Service Initialization"],"fullName":"StudentService Service Initialization should create StudentService instance","status":"passed","title":"should create StudentService instance","duration":8.780500000000302,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Service Initialization"],"fullName":"StudentService Service Initialization should inherit from BaseService","status":"passed","title":"should inherit from BaseService","duration":2.8753999999998996,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Create Student Operations"],"fullName":"StudentService Create Student Operations should create student successfully with valid data","status":"passed","title":"should create student successfully with valid data","duration":6.370199999999841,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Create Student Operations"],"fullName":"StudentService Create Student Operations should validate required fields","status":"passed","title":"should validate required fields","duration":3.4430999999999585,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Create Student Operations"],"fullName":"StudentService Create Student Operations should validate family exists","status":"passed","title":"should validate family exists","duration":3.0425999999997657,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Create Student Operations"],"fullName":"StudentService Create Student Operations should check for duplicate student ID","status":"passed","title":"should check for duplicate student ID","duration":4.980399999999918,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Create Student Operations"],"fullName":"StudentService Create Student Operations should handle database errors during creation","status":"passed","title":"should handle database errors during creation","duration":3.079699999999775,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Create Student Operations"],"fullName":"StudentService Create Student Operations should set default enrollment date if not provided","status":"passed","title":"should set default enrollment date if not provided","duration":4.859899999999925,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Find Student Operations"],"fullName":"StudentService Find Student Operations should find student by ID successfully","status":"passed","title":"should find student by ID successfully","duration":5.509799999999814,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Find Student Operations"],"fullName":"StudentService Find Student Operations should handle student not found","status":"passed","title":"should handle student not found","duration":3.3901999999998225,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Find Student Operations"],"fullName":"StudentService Find Student Operations should validate student ID format","status":"passed","title":"should validate student ID format","duration":3.8979000000003907,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Find Student Operations"],"fullName":"StudentService Find Student Operations should find students by family ID","status":"passed","title":"should find students by family ID","duration":5.078399999999874,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Update Student Operations"],"fullName":"StudentService Update Student Operations should update student successfully","status":"passed","title":"should update student successfully","duration":4.78690000000006,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Update Student Operations"],"fullName":"StudentService Update Student Operations should validate student exists before update","status":"passed","title":"should validate student exists before update","duration":3.965900000000147,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Update Student Operations"],"fullName":"StudentService Update Student Operations should check for duplicate student ID during update","status":"failed","title":"should check for duplicate student ID during update","duration":12.51260000000002,"failureMessages":["AssertionError: expected true to be false // Object.is equality\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\StudentService.test.ts:263:30\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/vitest/dist/chunks/runBaseTests.9YDrdSI4.js:130:11"],"meta":{}},{"ancestorTitles":["StudentService","Update Student Operations"],"fullName":"StudentService Update Student Operations should allow updating to same student ID","status":"passed","title":"should allow updating to same student ID","duration":2.4074000000000524,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Delete Student Operations"],"fullName":"StudentService Delete Student Operations should soft delete student successfully","status":"passed","title":"should soft delete student successfully","duration":2.5486000000000786,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Delete Student Operations"],"fullName":"StudentService Delete Student Operations should validate student exists before deletion","status":"passed","title":"should validate student exists before deletion","duration":4.56860000000006,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Delete Student Operations"],"fullName":"StudentService Delete Student Operations should prevent deletion of already inactive student","status":"passed","title":"should prevent deletion of already inactive student","duration":5.1194000000000415,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Search and Pagination"],"fullName":"StudentService Search and Pagination should find many students with default pagination","status":"passed","title":"should find many students with default pagination","duration":4.795300000000225,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Search and Pagination"],"fullName":"StudentService Search and Pagination should apply search filters correctly","status":"passed","title":"should apply search filters correctly","duration":11.283000000000357,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Search and Pagination"],"fullName":"StudentService Search and Pagination should handle date range filters","status":"passed","title":"should handle date range filters","duration":4.702499999999873,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Search and Pagination"],"fullName":"StudentService Search and Pagination should include inactive students when requested","status":"passed","title":"should include inactive students when requested","duration":6.621900000000096,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Statistics and Analytics"],"fullName":"StudentService Statistics and Analytics should get student statistics","status":"passed","title":"should get student statistics","duration":6.093299999999999,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Statistics and Analytics"],"fullName":"StudentService Statistics and Analytics should handle empty statistics gracefully","status":"passed","title":"should handle empty statistics gracefully","duration":3.7218000000002576,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Performance Testing"],"fullName":"StudentService Performance Testing should handle large datasets efficiently","status":"failed","title":"should handle large datasets efficiently","duration":5.725600000000213,"failureMessages":["AssertionError: expected 1000 to be less than or equal to 50\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\services\\StudentService.test.ts:480:41\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/vitest/dist/chunks/runBaseTests.9YDrdSI4.js:130:11"],"meta":{}},{"ancestorTitles":["StudentService","Performance Testing"],"fullName":"StudentService Performance Testing should track operation performance metrics","status":"passed","title":"should track operation performance metrics","duration":4.347200000000157,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Performance Testing"],"fullName":"StudentService Performance Testing should handle concurrent operations","status":"passed","title":"should handle concurrent operations","duration":40.57890000000043,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Input Validation and Edge Cases"],"fullName":"StudentService Input Validation and Edge Cases should handle invalid input data gracefully","status":"passed","title":"should handle invalid input data gracefully","duration":4.3344999999999345,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Input Validation and Edge Cases"],"fullName":"StudentService Input Validation and Edge Cases should handle edge case data","status":"passed","title":"should handle edge case data","duration":4.423899999999776,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Input Validation and Edge Cases"],"fullName":"StudentService Input Validation and Edge Cases should validate date ranges","status":"passed","title":"should validate date ranges","duration":3.7948999999998705,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Integration Testing"],"fullName":"StudentService Integration Testing should maintain data consistency across operations","status":"passed","title":"should maintain data consistency across operations","duration":9.139599999999973,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Integration Testing"],"fullName":"StudentService Integration Testing should handle complex search scenarios","status":"passed","title":"should handle complex search scenarios","duration":4.456700000000183,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Integration Testing"],"fullName":"StudentService Integration Testing should maintain referential integrity","status":"passed","title":"should maintain referential integrity","duration":3.7424000000000888,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Error Recovery and Resilience"],"fullName":"StudentService Error Recovery and Resilience should recover from temporary database failures","status":"passed","title":"should recover from temporary database failures","duration":2.731800000000021,"failureMessages":[],"meta":{}},{"ancestorTitles":["StudentService","Error Recovery and Resilience"],"fullName":"StudentService Error Recovery and Resilience should handle partial operation failures gracefully","status":"passed","title":"should handle partial operation failures gracefully","duration":4.325399999999718,"failureMessages":[],"meta":{}}],"startTime":1751348476793,"endTime":1751348477013.3254,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/services/StudentService.test.ts"},{"assertionResults":[{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should register all modules without errors","status":"passed","title":"should register all modules without errors","duration":9.56919999999991,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should register core modules first","status":"passed","title":"should register core modules first","duration":3.116899999999987,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should register feature modules with proper dependencies","status":"passed","title":"should register feature modules with proper dependencies","duration":3.6682999999998174,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should handle feature flag controlled modules","status":"passed","title":"should handle feature flag controlled modules","duration":1.7355999999999767,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should register integration modules conditionally","status":"passed","title":"should register integration modules conditionally","duration":1.9753000000000611,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Registration Process"],"fullName":"Module Registration System Module Registration Process should register experimental modules conditionally","status":"passed","title":"should register experimental modules conditionally","duration":1.8031000000000859,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should provide comprehensive module status","status":"passed","title":"should provide comprehensive module status","duration":2.7822000000001026,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should have correct module counts","status":"passed","title":"should have correct module counts","duration":2.2299000000002707,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should categorize modules correctly","status":"passed","title":"should categorize modules correctly","duration":2.42470000000003,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should track module status correctly","status":"passed","title":"should track module status correctly","duration":1.9719999999997526,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should identify enabled core modules","status":"passed","title":"should identify enabled core modules","duration":2.284599999999955,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Status and Statistics"],"fullName":"Module Registration System Module Status and Statistics should identify disabled feature-controlled modules","status":"passed","title":"should identify disabled feature-controlled modules","duration":2.4483000000000175,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Dependencies and Relationships"],"fullName":"Module Registration System Module Dependencies and Relationships should maintain proper dependency hierarchy","status":"passed","title":"should maintain proper dependency hierarchy","duration":2.2244000000000597,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Dependencies and Relationships"],"fullName":"Module Registration System Module Dependencies and Relationships should track module dependents correctly","status":"passed","title":"should track module dependents correctly","duration":5.601999999999862,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Dependencies and Relationships"],"fullName":"Module Registration System Module Dependencies and Relationships should prevent disabling modules with active dependents","status":"passed","title":"should prevent disabling modules with active dependents","duration":0.9416000000001077,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Feature Flag Integration"],"fullName":"Module Registration System Feature Flag Integration should respect feature flag states during registration","status":"failed","title":"should respect feature flag states during registration","duration":16.68399999999974,"failureMessages":["AssertionError: expected true to be false // Object.is equality\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\modules\\ModuleRegistration.test.ts:254:53\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/vitest/dist/chunks/runBaseTests.9YDrdSI4.js:130:11"],"meta":{}},{"ancestorTitles":["Module Registration System","Feature Flag Integration"],"fullName":"Module Registration System Feature Flag Integration should handle modules with mixed feature requirements","status":"passed","title":"should handle modules with mixed feature requirements","duration":0.8292999999998756,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Performance and Scalability"],"fullName":"Module Registration System Performance and Scalability should register modules efficiently","status":"passed","title":"should register modules efficiently","duration":0.910200000000259,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Performance and Scalability"],"fullName":"Module Registration System Performance and Scalability should provide status information efficiently","status":"passed","title":"should provide status information efficiently","duration":0.648899999999685,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Performance and Scalability"],"fullName":"Module Registration System Performance and Scalability should handle module queries efficiently","status":"passed","title":"should handle module queries efficiently","duration":1.697400000000016,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Error Handling and Resilience"],"fullName":"Module Registration System Error Handling and Resilience should handle registration errors gracefully","status":"passed","title":"should handle registration errors gracefully","duration":0.8130999999998494,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Error Handling and Resilience"],"fullName":"Module Registration System Error Handling and Resilience should provide error information in status","status":"passed","title":"should provide error information in status","duration":0.8096999999997934,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Error Handling and Resilience"],"fullName":"Module Registration System Error Handling and Resilience should handle missing dependencies gracefully","status":"passed","title":"should handle missing dependencies gracefully","duration":1.3498999999997068,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Health and Monitoring"],"fullName":"Module Registration System Module Health and Monitoring should provide health status for modules","status":"passed","title":"should provide health status for modules","duration":2.578399999999874,"failureMessages":[],"meta":{}},{"ancestorTitles":["Module Registration System","Module Health and Monitoring"],"fullName":"Module Registration System Module Health and Monitoring should track module performance metrics","status":"passed","title":"should track module performance metrics","duration":1.2414000000003398,"failureMessages":[],"meta":{}}],"startTime":1751348476779,"endTime":1751348476853.2415,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/modules/ModuleRegistration.test.ts"},{"assertionResults":[{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should register a valid module successfully","status":"passed","title":"should register a valid module successfully","duration":6.523099999999886,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should validate module configuration","status":"passed","title":"should validate module configuration","duration":2.024499999999989,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should prevent duplicate module registration","status":"passed","title":"should prevent duplicate module registration","duration":1.1589999999996508,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should validate dependencies exist","status":"passed","title":"should validate dependencies exist","duration":0.9057000000002517,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should detect circular dependencies","status":"failed","title":"should detect circular dependencies","duration":8.857300000000123,"failureMessages":["Error: Dependency module-b not found for module module-a\n    at ModuleRegistry.register (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\lib\\modules\\ModuleRegistry.ts:202:17)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\modules\\ModuleRegistry.test.ts:148:16\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should handle feature flag requirements","status":"passed","title":"should handle feature flag requirements","duration":7.111499999999978,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Registration"],"fullName":"ModuleRegistry Module Registration should set module metadata correctly","status":"passed","title":"should set module metadata correctly","duration":1.20049999999992,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should check if module is enabled","status":"passed","title":"should check if module is enabled","duration":0.9690000000000509,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should get all modules","status":"passed","title":"should get all modules","duration":2.0955000000003565,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should get enabled modules only","status":"passed","title":"should get enabled modules only","duration":1.1344999999996617,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should get modules by category","status":"passed","title":"should get modules by category","duration":1.106800000000021,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should get module dependencies","status":"passed","title":"should get module dependencies","duration":2.748099999999795,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should get module dependents","status":"passed","title":"should get module dependents","duration":1.2024999999998727,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Querying and Management"],"fullName":"ModuleRegistry Module Querying and Management should track access metrics","status":"passed","title":"should track access metrics","duration":1.6801000000000386,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should check if module can be disabled","status":"passed","title":"should check if module can be disabled","duration":1.5688999999997577,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should disable module without dependents","status":"passed","title":"should disable module without dependents","duration":1.0316000000002532,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should prevent disabling module with dependents","status":"passed","title":"should prevent disabling module with dependents","duration":0.6293000000000575,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should enable disabled module","status":"passed","title":"should enable disabled module","duration":0.951799999999821,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should validate dependencies when enabling","status":"passed","title":"should validate dependencies when enabling","duration":4.018099999999777,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Module Enable/Disable Operations"],"fullName":"ModuleRegistry Module Enable/Disable Operations should validate feature flags when enabling","status":"passed","title":"should validate feature flags when enabling","duration":0.8037999999996828,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Event System"],"fullName":"ModuleRegistry Event System should emit registration events","status":"passed","title":"should emit registration events","duration":2.707100000000082,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Event System"],"fullName":"ModuleRegistry Event System should emit enable/disable events","status":"passed","title":"should emit enable/disable events","duration":1.5852999999997337,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Event System"],"fullName":"ModuleRegistry Event System should handle event listener errors gracefully","status":"passed","title":"should handle event listener errors gracefully","duration":0.9259000000001834,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Event System"],"fullName":"ModuleRegistry Event System should remove event listeners","status":"passed","title":"should remove event listeners","duration":0.7092999999999847,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Health Monitoring"],"fullName":"ModuleRegistry Health Monitoring should perform health checks on all modules","status":"passed","title":"should perform health checks on all modules","duration":1.121599999999944,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Health Monitoring"],"fullName":"ModuleRegistry Health Monitoring should emit health check events","status":"passed","title":"should emit health check events","duration":0.7019000000000233,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Health Monitoring"],"fullName":"ModuleRegistry Health Monitoring should detect dependency health issues","status":"passed","title":"should detect dependency health issues","duration":0.6246000000001004,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Statistics and Performance"],"fullName":"ModuleRegistry Statistics and Performance should provide comprehensive statistics","status":"passed","title":"should provide comprehensive statistics","duration":1.544499999999971,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Statistics and Performance"],"fullName":"ModuleRegistry Statistics and Performance should track memory usage estimates","status":"passed","title":"should track memory usage estimates","duration":1.0093000000001666,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Statistics and Performance"],"fullName":"ModuleRegistry Statistics and Performance should track load time performance","status":"passed","title":"should track load time performance","duration":0.8958000000002357,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Error Handling and Edge Cases"],"fullName":"ModuleRegistry Error Handling and Edge Cases should handle invalid module configurations gracefully","status":"passed","title":"should handle invalid module configurations gracefully","duration":1.940099999999802,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Error Handling and Edge Cases"],"fullName":"ModuleRegistry Error Handling and Edge Cases should handle non-existent module operations","status":"passed","title":"should handle non-existent module operations","duration":1.2333999999996195,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Error Handling and Edge Cases"],"fullName":"ModuleRegistry Error Handling and Edge Cases should handle complex dependency chains","status":"passed","title":"should handle complex dependency chains","duration":1.1190000000001419,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Error Handling and Edge Cases"],"fullName":"ModuleRegistry Error Handling and Edge Cases should handle module registration errors","status":"passed","title":"should handle module registration errors","duration":1.2285000000001673,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Integration with Feature Flags"],"fullName":"ModuleRegistry Integration with Feature Flags should respect feature flag changes","status":"failed","title":"should respect feature flag changes","duration":8.104400000000169,"failureMessages":["TypeError: isFeatureEnabled.mockReturnValue is not a function\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\modules\\ModuleRegistry.test.ts:524:24\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)\n    at startTests (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1197:3)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/vitest/dist/chunks/runBaseTests.9YDrdSI4.js:130:11"],"meta":{}},{"ancestorTitles":["ModuleRegistry","Integration with Feature Flags"],"fullName":"ModuleRegistry Integration with Feature Flags should handle optional features correctly","status":"passed","title":"should handle optional features correctly","duration":4.971700000000055,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Performance Benchmarks"],"fullName":"ModuleRegistry Performance Benchmarks should register modules efficiently","status":"passed","title":"should register modules efficiently","duration":2.5351999999998043,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Performance Benchmarks"],"fullName":"ModuleRegistry Performance Benchmarks should query modules efficiently","status":"passed","title":"should query modules efficiently","duration":3.6043000000004213,"failureMessages":[],"meta":{}},{"ancestorTitles":["ModuleRegistry","Performance Benchmarks"],"fullName":"ModuleRegistry Performance Benchmarks should handle health checks efficiently","status":"passed","title":"should handle health checks efficiently","duration":1.903400000000147,"failureMessages":[],"meta":{}}],"startTime":1751348476782,"endTime":1751348476869.9033,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/modules/ModuleRegistry.test.ts"},{"assertionResults":[{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Feature Flags System"],"fullName":"Phase 1 Implementation - Simple Validation Feature Flags System should import feature flags module without errors","status":"passed","title":"should import feature flags module without errors","duration":58.970600000000104,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Feature Flags System"],"fullName":"Phase 1 Implementation - Simple Validation Feature Flags System should provide isFeatureEnabled function","status":"passed","title":"should provide isFeatureEnabled function","duration":8.944700000000012,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Feature Flags System"],"fullName":"Phase 1 Implementation - Simple Validation Feature Flags System should provide getAllFeatureFlags function","status":"passed","title":"should provide getAllFeatureFlags function","duration":6.473299999999654,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Feature Flags System"],"fullName":"Phase 1 Implementation - Simple Validation Feature Flags System should return boolean values for feature flags","status":"passed","title":"should return boolean values for feature flags","duration":7.453399999999874,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Feature Flags System"],"fullName":"Phase 1 Implementation - Simple Validation Feature Flags System should return complete feature flags object","status":"passed","title":"should return complete feature flags object","duration":9.566200000000208,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Feature Flags System"],"fullName":"Phase 1 Implementation - Simple Validation Feature Flags System should provide analytics functionality","status":"passed","title":"should provide analytics functionality","duration":8.376299999999901,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Service Layer Implementation"],"fullName":"Phase 1 Implementation - Simple Validation Service Layer Implementation should import BaseService without errors","status":"passed","title":"should import BaseService without errors","duration":8.424199999999928,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Service Layer Implementation"],"fullName":"Phase 1 Implementation - Simple Validation Service Layer Implementation should import StudentService without errors","status":"passed","title":"should import StudentService without errors","duration":7.029100000000199,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Service Layer Implementation"],"fullName":"Phase 1 Implementation - Simple Validation Service Layer Implementation should provide BaseService class","status":"passed","title":"should provide BaseService class","duration":7.8840000000000146,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Service Layer Implementation"],"fullName":"Phase 1 Implementation - Simple Validation Service Layer Implementation should provide StudentService class","status":"passed","title":"should provide StudentService class","duration":11.541400000000067,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Service Layer Implementation"],"fullName":"Phase 1 Implementation - Simple Validation Service Layer Implementation should create StudentService instance","status":"passed","title":"should create StudentService instance","duration":12.485699999999724,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Service Layer Implementation"],"fullName":"Phase 1 Implementation - Simple Validation Service Layer Implementation should provide required service methods","status":"passed","title":"should provide required service methods","duration":36.589300000000094,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Service Layer Implementation"],"fullName":"Phase 1 Implementation - Simple Validation Service Layer Implementation should provide ServiceResult interface types","status":"passed","title":"should provide ServiceResult interface types","duration":9.99850000000015,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should import ModuleRegistry without errors","status":"passed","title":"should import ModuleRegistry without errors","duration":6.795700000000124,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should import module registration without errors","status":"passed","title":"should import module registration without errors","duration":17.71270000000004,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should provide ModuleRegistry class","status":"passed","title":"should provide ModuleRegistry class","duration":7.846999999999753,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should provide global moduleRegistry instance","status":"passed","title":"should provide global moduleRegistry instance","duration":8.552799999999934,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should provide module registration functions","status":"passed","title":"should provide module registration functions","duration":23.203399999999874,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should register modules successfully","status":"passed","title":"should register modules successfully","duration":11.107099999999718,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should provide module status information","status":"passed","title":"should provide module status information","duration":12.384999999999764,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should handle module dependencies","status":"passed","title":"should handle module dependencies","duration":8.739500000000135,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Module Registry System"],"fullName":"Phase 1 Implementation - Simple Validation Module Registry System should provide module statistics","status":"passed","title":"should provide module statistics","duration":9.379199999999855,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","System Integration"],"fullName":"Phase 1 Implementation - Simple Validation System Integration should integrate all Phase 1 components without errors","status":"passed","title":"should integrate all Phase 1 components without errors","duration":5.364300000000185,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","System Integration"],"fullName":"Phase 1 Implementation - Simple Validation System Integration should maintain backwards compatibility","status":"passed","title":"should maintain backwards compatibility","duration":9.852100000000064,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","System Integration"],"fullName":"Phase 1 Implementation - Simple Validation System Integration should provide consistent interfaces","status":"passed","title":"should provide consistent interfaces","duration":14.715899999999692,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Phase 1 Success Criteria"],"fullName":"Phase 1 Implementation - Simple Validation Phase 1 Success Criteria should meet all Phase 1 success criteria","status":"failed","title":"should meet all Phase 1 success criteria","duration":34.220700000000306,"failureMessages":["AssertionError: expected [Function] to not throw an error but 'Error: Cannot find module \\'@/lib/conΓÇª' was thrown\n    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/expect/dist/index.js:1437:21)\n    at Proxy.<anonymous> (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/expect/dist/index.js:923:17)\n    at Proxy.methodWrapper (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/chai/chai.js:1618:25)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\simple\\Phase1Validation.test.ts:311:14\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:5\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:11)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)"],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Phase 1 Success Criteria"],"fullName":"Phase 1 Implementation - Simple Validation Phase 1 Success Criteria should provide development experience improvements","status":"passed","title":"should provide development experience improvements","duration":14.031199999999899,"failureMessages":[],"meta":{}},{"ancestorTitles":["Phase 1 Implementation - Simple Validation","Phase 1 Success Criteria"],"fullName":"Phase 1 Implementation - Simple Validation Phase 1 Success Criteria should maintain performance standards","status":"passed","title":"should maintain performance standards","duration":15.357500000000073,"failureMessages":[],"meta":{}}],"startTime":1751348476720,"endTime":1751348477115.3574,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/simple/Phase1Validation.test.ts"},{"assertionResults":[{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlag Hook"],"fullName":"Feature Flag React Hooks useFeatureFlag Hook should return correct feature flag value","status":"failed","title":"should return correct feature flag value","duration":33.53540000000021,"failureMessages":["AssertionError: expected true to be false // Object.is equality\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\__tests__\\hooks\\shared\\useFeatureFlag.test.tsx:103:30\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:146:14\n    at file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:529:11\n    at runWithTimeout (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:61:7)\n    at runTest (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:982:17)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runSuite (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1131:15)\n    at runFiles (file:///C:/Users/<USER>/OneDrive/Desktop/geminicoder/node_modules/@vitest/runner/dist/index.js:1188:5)"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlag Hook"],"fullName":"Feature Flag React Hooks useFeatureFlag Hook should return false for disabled features","status":"passed","title":"should return false for disabled features","duration":6.92009999999982,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlag Hook"],"fullName":"Feature Flag React Hooks useFeatureFlag Hook should handle SSR safely","status":"failed","title":"should handle SSR safely","duration":15.950800000000072,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","ReferenceError: window is not defined\n    at getActiveElementDeep (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:8442:13)\n    at getSelectionInformation (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:8476:21)\n    at prepareForCommit (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10912:26)\n    at commitBeforeMutationEffects (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:22980:27)\n    at commitRootImpl (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26840:45)\n    at commitRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26721:5)\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26156:3)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushSync (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26240:7)\n    at ReactDOMRoot.ReactDOMHydrationRoot.unmount.ReactDOMRoot.unmount [as unmount] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29375:5)"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlags Hook"],"fullName":"Feature Flag React Hooks useFeatureFlags Hook should return multiple feature flags","status":"failed","title":"should return multiple feature flags","duration":2.8490999999999076,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlags Hook"],"fullName":"Feature Flag React Hooks useFeatureFlags Hook should handle empty feature array","status":"failed","title":"should handle empty feature array","duration":2.0632000000000517,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useAllFeatureFlags Hook"],"fullName":"Feature Flag React Hooks useAllFeatureFlags Hook should return all feature flags","status":"failed","title":"should return all feature flags","duration":1.5614999999997963,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useEnabledFeatures Hook"],"fullName":"Feature Flag React Hooks useEnabledFeatures Hook should return only enabled features","status":"failed","title":"should return only enabled features","duration":1.7998999999999796,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlagAnalytics Hook"],"fullName":"Feature Flag React Hooks useFeatureFlagAnalytics Hook should return analytics data","status":"failed","title":"should return analytics data","duration":3.3485999999998057,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlagValidation Hook"],"fullName":"Feature Flag React Hooks useFeatureFlagValidation Hook should return validation results","status":"failed","title":"should return validation results","duration":3.233900000000176,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlagValidation Hook"],"fullName":"Feature Flag React Hooks useFeatureFlagValidation Hook should handle validation errors","status":"failed","title":"should handle validation errors","duration":3.2500999999997475,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","withFeatureFlag HOC"],"fullName":"Feature Flag React Hooks withFeatureFlag HOC should render component when feature is enabled","status":"failed","title":"should render component when feature is enabled","duration":4.460900000000038,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","withFeatureFlag HOC"],"fullName":"Feature Flag React Hooks withFeatureFlag HOC should not render component when feature is disabled","status":"failed","title":"should not render component when feature is disabled","duration":4.146700000000237,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","withFeatureFlag HOC"],"fullName":"Feature Flag React Hooks withFeatureFlag HOC should render fallback component when feature is disabled","status":"failed","title":"should render fallback component when feature is disabled","duration":3.2134000000000924,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","FeatureGate Component"],"fullName":"Feature Flag React Hooks FeatureGate Component should render children when feature is enabled","status":"failed","title":"should render children when feature is enabled","duration":3.4513999999999214,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","FeatureGate Component"],"fullName":"Feature Flag React Hooks FeatureGate Component should not render children when feature is disabled","status":"failed","title":"should not render children when feature is disabled","duration":2.1882000000000517,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","FeatureGate Component"],"fullName":"Feature Flag React Hooks FeatureGate Component should render fallback when feature is disabled","status":"failed","title":"should render fallback when feature is disabled","duration":2.5579999999999927,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","FeatureDisabled Component"],"fullName":"Feature Flag React Hooks FeatureDisabled Component should render children when feature is disabled","status":"failed","title":"should render children when feature is disabled","duration":4.913300000000163,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","FeatureDisabled Component"],"fullName":"Feature Flag React Hooks FeatureDisabled Component should not render children when feature is enabled","status":"failed","title":"should not render children when feature is enabled","duration":2.6429000000002816,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","useFeatureFlagDebug Hook"],"fullName":"Feature Flag React Hooks useFeatureFlagDebug Hook should provide debug functions","status":"failed","title":"should provide debug functions","duration":2.407199999999648,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","Utility Functions"],"fullName":"Feature Flag React Hooks Utility Functions isValidFeatureFlag should validate feature names","status":"passed","title":"isValidFeatureFlag should validate feature names","duration":2.6428000000000793,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","Utility Functions"],"fullName":"Feature Flag React Hooks Utility Functions createFeatureDependentValue should return correct values","status":"passed","title":"createFeatureDependentValue should return correct values","duration":2.2060999999998785,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","Performance and Memory"],"fullName":"Feature Flag React Hooks Performance and Memory should not cause memory leaks with multiple renders","status":"failed","title":"should not cause memory leaks with multiple renders","duration":1.910099999999602,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}},{"ancestorTitles":["Feature Flag React Hooks","Performance and Memory"],"fullName":"Feature Flag React Hooks Performance and Memory should memoize results correctly","status":"failed","title":"should memoize results correctly","duration":2.5261999999997897,"failureMessages":["ReferenceError: window is not defined\n    at getCurrentEventPriority (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:10993:22)\n    at requestUpdateLane (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:25495:19)\n    at updateContainer (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:28854:14)\n    at ReactDOMRoot.ReactDOMHydrationRoot.render.ReactDOMRoot.render [as render] (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:29353:3)\n    at Object.render (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:122:12)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:163:12\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:64:24\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2512:16)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25","Error: Should not already be working.\n    at performSyncWorkOnRoot (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:26112:11)\n    at flushSyncCallbacks (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom.development.js:12042:22)\n    at flushActQueue (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at actWithWarning (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\react-dom\\cjs\\react-dom-test-utils.development.js:1740:10)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\act-compat.js:63:25\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:261:28\n    at Array.forEach (<anonymous>)\n    at cleanup (C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\pure.js:257:22)\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\geminicoder\\node_modules\\@testing-library\\react\\dist\\index.js:35:25"],"meta":{}}],"startTime":1751348476797,"endTime":1751348476914.5261,"status":"failed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/hooks/shared/useFeatureFlag.test.tsx"},{"assertionResults":[{"ancestorTitles":["Feature Flags System","Environment Variable Parsing"],"fullName":"Feature Flags System Environment Variable Parsing should parse true values correctly","status":"passed","title":"should parse true values correctly","duration":6.418599999999969,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Environment Variable Parsing"],"fullName":"Feature Flags System Environment Variable Parsing should parse false values correctly","status":"passed","title":"should parse false values correctly","duration":7.623700000000099,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Environment Variable Parsing"],"fullName":"Feature Flags System Environment Variable Parsing should handle case insensitive values","status":"passed","title":"should handle case insensitive values","duration":3.330699999999979,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Environment Variable Parsing"],"fullName":"Feature Flags System Environment Variable Parsing should use default values for undefined environment variables","status":"passed","title":"should use default values for undefined environment variables","duration":3.7881999999999607,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Environment Variable Parsing"],"fullName":"Feature Flags System Environment Variable Parsing should handle invalid environment variable values","status":"passed","title":"should handle invalid environment variable values","duration":2.6449000000002343,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Functions"],"fullName":"Feature Flags System Feature Flag Functions isFeatureEnabled should return correct boolean values","status":"passed","title":"isFeatureEnabled should return correct boolean values","duration":0.922099999999773,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Functions"],"fullName":"Feature Flags System Feature Flag Functions getAllFeatureFlags should return complete FeatureFlags object","status":"passed","title":"getAllFeatureFlags should return complete FeatureFlags object","duration":1.6280000000001564,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Functions"],"fullName":"Feature Flags System Feature Flag Functions getEnabledFeatures should return only enabled features","status":"passed","title":"getEnabledFeatures should return only enabled features","duration":6.922299999999723,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Analytics"],"fullName":"Feature Flags System Feature Flag Analytics getFeatureFlagAnalytics should return correct analytics data","status":"passed","title":"getFeatureFlagAnalytics should return correct analytics data","duration":2.477300000000014,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Analytics"],"fullName":"Feature Flags System Feature Flag Analytics analytics should update when flags change","status":"passed","title":"analytics should update when flags change","duration":6.27980000000025,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Validation"],"fullName":"Feature Flags System Feature Flag Validation should validate production environment constraints","status":"passed","title":"should validate production environment constraints","duration":4.649699999999939,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Validation"],"fullName":"Feature Flags System Feature Flag Validation should validate feature dependencies","status":"passed","title":"should validate feature dependencies","duration":6.008899999999812,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Feature Flag Validation"],"fullName":"Feature Flags System Feature Flag Validation should pass validation with correct configuration","status":"passed","title":"should pass validation with correct configuration","duration":5.560100000000148,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Development Mode Features"],"fullName":"Feature Flags System Development Mode Features should enable debug mode automatically in development","status":"passed","title":"should enable debug mode automatically in development","duration":5.71140000000014,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Development Mode Features"],"fullName":"Feature Flags System Development Mode Features should respect explicit debug mode setting","status":"passed","title":"should respect explicit debug mode setting","duration":4.725300000000061,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Edge Cases"],"fullName":"Feature Flags System Edge Cases should handle empty string environment variables","status":"passed","title":"should handle empty string environment variables","duration":5.534599999999955,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Edge Cases"],"fullName":"Feature Flags System Edge Cases should handle whitespace in environment variables","status":"passed","title":"should handle whitespace in environment variables","duration":4.872400000000198,"failureMessages":[],"meta":{}},{"ancestorTitles":["Feature Flags System","Edge Cases"],"fullName":"Feature Flags System Edge Cases should handle all feature flag keys","status":"passed","title":"should handle all feature flag keys","duration":3.238299999999981,"failureMessages":[],"meta":{}}],"startTime":1751348476744,"endTime":1751348476827.2383,"status":"passed","message":"","name":"C:/Users/<USER>/OneDrive/Desktop/geminicoder/__tests__/lib/config/FeatureFlags.test.ts"}]}
