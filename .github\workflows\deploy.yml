# =============================================================================
# RK Institute Management System - Deployment Pipeline
# =============================================================================
# This workflow handles automated deployments to staging and production
# environments based on branch pushes and successful CI checks.

name: 🚀 Deployment Pipeline

on:
  push:
    branches:
      - main # Production deployment
      - develop # Staging deployment
  workflow_run:
    workflows: ['🔄 Continuous Integration']
    types: [completed]
    branches: [main, develop]

# Ensure only one deployment runs at a time
concurrency:
  group: deploy-${{ github.ref }}
  cancel-in-progress: false

jobs:
  # =============================================================================
  # Pre-deployment Checks
  # =============================================================================
  pre-deployment:
    name: 🔍 Pre-deployment Checks
    runs-on: ubuntu-latest
    if: github.event.workflow_run.conclusion == 'success' || github.event_name == 'push'

    outputs:
      environment: ${{ steps.determine-env.outputs.environment }}
      deploy-url: ${{ steps.determine-env.outputs.deploy-url }}

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🎯 Determine deployment environment
        id: determine-env
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "environment=production" >> $GITHUB_OUTPUT
            echo "deploy-url=https://rk-institute-management-system.vercel.app" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/develop" ]]; then
            echo "environment=staging" >> $GITHUB_OUTPUT
            echo "deploy-url=https://rk-institute-management-system-staging.vercel.app" >> $GITHUB_OUTPUT
          fi

      - name: 📊 Display deployment info
        run: |
          echo "🎯 Target Environment: ${{ steps.determine-env.outputs.environment }}"
          echo "🌐 Deployment URL: ${{ steps.determine-env.outputs.deploy-url }}"
          echo "📝 Commit SHA: ${{ github.sha }}"
          echo "👤 Triggered by: ${{ github.actor }}"

  # =============================================================================
  # Production Deployment
  # =============================================================================
  deploy-production:
    name: 🌟 Production Deployment
    runs-on: ubuntu-latest
    needs: pre-deployment
    if: needs.pre-deployment.outputs.environment == 'production'
    environment:
      name: production
      url: ${{ needs.pre-deployment.outputs.deploy-url }}

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🔧 Generate Prisma client
        run: npx prisma generate

      - name: 🏗️ Build application
        run: npm run build

      - name: 🚀 Deploy to Vercel (Production)
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
          working-directory: ./

      - name: 🔍 Post-deployment health check
        run: |
          echo "🔍 Running post-deployment health checks..."
          sleep 30  # Wait for deployment to stabilize

          # Health check with retry logic
          for i in {1..5}; do
            if curl -f -s "${{ needs.pre-deployment.outputs.deploy-url }}/api/health" > /dev/null; then
              echo "✅ Health check passed on attempt $i"
              break
            else
              echo "⚠️ Health check failed on attempt $i, retrying..."
              sleep 10
            fi

            if [ $i -eq 5 ]; then
              echo "❌ Health check failed after 5 attempts"
              exit 1
            fi
          done

      - name: 🧪 Production smoke tests
        run: |
          echo "🧪 Running production smoke tests..."

          # Test critical endpoints
          curl -f -s "${{ needs.pre-deployment.outputs.deploy-url }}/api/health" || exit 1
          curl -f -s "${{ needs.pre-deployment.outputs.deploy-url }}/api/health/database" || exit 1

          echo "✅ All smoke tests passed"

      - name: 📊 Deployment success notification
        run: |
          echo "🎉 Production deployment successful!"
          echo "🌐 Live URL: ${{ needs.pre-deployment.outputs.deploy-url }}"
          echo "📝 Commit: ${{ github.sha }}"
          echo "🔍 Health Status: Verified"
          echo "🧪 Smoke Tests: Passed"

  # =============================================================================
  # Staging Deployment
  # =============================================================================
  deploy-staging:
    name: 🧪 Staging Deployment
    runs-on: ubuntu-latest
    needs: pre-deployment
    if: needs.pre-deployment.outputs.environment == 'staging'
    environment:
      name: staging
      url: ${{ needs.pre-deployment.outputs.deploy-url }}

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🔧 Generate Prisma client
        run: npx prisma generate

      - name: 🏗️ Build application
        run: npm run build

      - name: 🧪 Deploy to Vercel (Staging)
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./

      - name: 🔍 Staging health check
        run: |
          echo "🔍 Running staging health checks..."
          sleep 20  # Wait for staging deployment to stabilize

          # Health check for staging
          STAGING_URL="${{ needs.pre-deployment.outputs.deploy-url }}"
          if [[ "$STAGING_URL" == *"staging"* ]]; then
            curl -f -s "$STAGING_URL/api/health" || echo "⚠️ Staging health check failed (non-blocking)"
          fi

      - name: 📊 Staging deployment notification
        run: |
          echo "🧪 Staging deployment successful!"
          echo "🌐 Preview URL: ${{ needs.pre-deployment.outputs.deploy-url }}"
          echo "📝 Commit: ${{ github.sha }}"
          echo "🔍 Health Check: Completed"

  # =============================================================================
  # Post-deployment Health Checks
  # =============================================================================
  health-check:
    name: 🏥 Health Check
    runs-on: ubuntu-latest
    needs: [pre-deployment, deploy-production, deploy-staging]
    if: always() && (needs.deploy-production.result == 'success' || needs.deploy-staging.result == 'success')

    steps:
      - name: 🏥 Wait for deployment to be ready
        run: sleep 30

      - name: 🔍 Health check endpoint
        run: |
          URL="${{ needs.pre-deployment.outputs.deploy-url }}"
          echo "🔍 Checking health of: $URL"

          # Basic connectivity check
          if curl -f -s "$URL" > /dev/null; then
            echo "✅ Application is responding"
          else
            echo "❌ Application health check failed"
            exit 1
          fi

      - name: 📊 Deployment summary
        run: |
          echo "🎯 Environment: ${{ needs.pre-deployment.outputs.environment }}"
          echo "🌐 URL: ${{ needs.pre-deployment.outputs.deploy-url }}"
          echo "✅ Health check: PASSED"
          echo "🕐 Deployed at: $(date)"
